# 昆明项目自动化截图工具

这是一个专为昆明项目设计的全面自动化截图解决方案，用于支持国际化工作中的界面截图收集需求。

## 🎯 功能特性

### 核心功能
- **全面覆盖**: 自动遍历所有页面和组件，确保100%界面覆盖
- **多状态截图**: 支持加载中、错误、空数据、成功等多种界面状态
- **响应式支持**: 支持不同屏幕尺寸和设备方向的截图
- **权限差异**: 支持不同用户权限下的界面差异截图
- **智能组织**: 按模块、页面、状态自动分类组织截图
- **详细报告**: 生成包含元数据的HTML报告

### 技术特性
- **自动化程度高**: 基于UI Automator 2.0，支持完全自动化执行
- **配置灵活**: 通过YAML配置文件灵活控制截图范围和参数
- **稳定可靠**: 内置重试机制和异常处理，确保执行稳定性
- **扩展性强**: 模块化设计，易于扩展和定制

## 📋 系统要求

### 开发环境
- Android Studio 4.0+
- JDK 8+
- Android SDK API 21+
- Gradle 7.0+

### 设备要求
- Android 5.0+ (API 21+)
- 启用USB调试
- 启用"允许模拟点击"权限

### 依赖库
- UI Automator 2.0
- Espresso
- Jackson (YAML/JSON处理)
- Kotlin协程

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <项目地址>
cd kunming

# 确保Android设备已连接并启用USB调试
adb devices

# 检查设备状态
adb shell getprop ro.build.version.release
```

### 2. 配置设置

编辑配置文件 `screenshot_automation/config/screenshot_config.yaml`:

```yaml
# 基本项目信息
project:
  name: "昆明项目"
  package_name: "com.superhexa.supervision"
  main_activity: "com.superhexa.supervision.NavHostActivity"

# 设备配置 - 根据需要调整
devices:
  phone:
    - name: "标准手机"
      width: 414
      height: 896
      density: 3.0

# 输出配置
output:
  base_path: "./screenshots"
  format: "png"
  quality: 100
```

### 3. 执行截图

```bash
# 进入截图工具目录
cd screenshot_automation

# 执行完整截图流程
./run_screenshots.sh

# 或者使用Gradle任务
cd ..
./gradlew :screenshot_automation:fullScreenshotProcess
```

### 4. 查看结果

截图完成后，查看输出目录：
```
screenshots/
├── home/
│   ├── HomeFragment/
│   │   ├── states/
│   │   │   ├── home_HomeFragment_loading_标准手机_20231201_143022.png
│   │   │   ├── home_HomeFragment_success_标准手机_20231201_143025.png
│   │   │   └── ...
│   │   └── metadata/
│   └── DeviceAddFragment/
├── login/
├── screenshot_report.html
└── screenshot_summary.txt
```

## 📖 详细使用指南

### 命令行选项

```bash
./run_screenshots.sh [选项]

选项:
  -h, --help              显示帮助信息
  -c, --config FILE       指定配置文件路径
  -o, --output DIR        指定输出目录
  -d, --device DEVICE     指定设备ID
  -m, --modules MODULES   指定要截图的模块 (逗号分隔)
  -s, --states STATES     指定要截图的状态 (逗号分隔)
  --clean                 执行前清理输出目录
  --debug                 启用调试模式
  --dry-run              预览执行计划
```

### 使用示例

```bash
# 完整截图流程
./run_screenshots.sh --clean --debug

# 只截图特定模块
./run_screenshots.sh -m home,login

# 只截图特定状态
./run_screenshots.sh -s loading,success,error

# 指定设备执行
./run_screenshots.sh -d emulator-5554

# 预览执行计划
./run_screenshots.sh --dry-run
```

### 配置文件详解

#### 项目配置
```yaml
project:
  name: "昆明项目"                    # 项目名称
  package_name: "com.superhexa.supervision"  # 应用包名
  main_activity: "com.superhexa.supervision.NavHostActivity"  # 主Activity
```

#### 设备配置
```yaml
devices:
  phone:
    - name: "小屏手机"
      width: 360
      height: 640
      density: 2.0
    - name: "大屏手机"
      width: 428
      height: 926
      density: 3.0
  tablet:
    - name: "平板"
      width: 1024
      height: 1366
      density: 2.0
```

#### 模块配置
```yaml
modules:
  home:
    name: "首页"
    fragments:
      - name: "HomeFragment"
        route: "/home/<USER>"
        states: ["loading", "empty", "with_templates"]
```

#### 执行配置
```yaml
execution:
  page_load_timeout: 5000      # 页面加载超时时间(ms)
  state_change_timeout: 3000   # 状态切换超时时间(ms)
  screenshot_interval: 1000    # 截图间隔时间(ms)
  retry_count: 3               # 失败重试次数
```

## 🔧 高级功能

### 自定义状态策略

可以为特定状态实现自定义策略：

```kotlin
class CustomLoadingStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        // 自定义加载状态设置逻辑
        return StateSetResult.success("自定义加载状态设置成功")
    }
}
```

### 扩展页面发现

可以扩展页面自动发现逻辑：

```kotlin
class CustomPageDiscovery : PageDiscovery {
    override fun discoverPages(): List<PageTarget> {
        // 自定义页面发现逻辑
        return listOf(/* 发现的页面 */)
    }
}
```

### 自定义截图处理

可以添加自定义截图后处理：

```kotlin
class CustomScreenshotProcessor : ScreenshotProcessor {
    override fun processScreenshot(screenshotFile: File): ProcessResult {
        // 自定义截图处理逻辑（如压缩、水印等）
        return ProcessResult.success()
    }
}
```

## 📊 报告和分析

### HTML报告

工具会自动生成包含以下信息的HTML报告：
- 截图总览和统计
- 按模块分类的截图展示
- 每张截图的详细元数据
- 设备信息和执行时间

### 元数据

每张截图都包含JSON格式的元数据：
```json
{
  "screenshot_file": "home_HomeFragment_loading_标准手机_20231201_143022.png",
  "module": "home",
  "fragment": "HomeFragment",
  "state": "loading",
  "device": "标准手机",
  "device_width": 414,
  "device_height": 896,
  "timestamp": "2023-12-01 14:30:22",
  "file_size": 245760
}
```

## 🐛 故障排除

### 常见问题

1. **设备连接问题**
   ```bash
   # 检查设备连接
   adb devices
   
   # 重启ADB服务
   adb kill-server
   adb start-server
   ```

2. **权限问题**
   ```bash
   # 确保启用了必要权限
   adb shell settings put secure enabled_accessibility_services com.superhexa.supervision/com.superhexa.screenshot.AccessibilityService
   ```

3. **应用安装问题**
   ```bash
   # 手动安装应用
   ./gradlew installDebug installDebugAndroidTest
   ```

4. **截图失败**
   - 检查设备屏幕是否亮起
   - 确保应用在前台运行
   - 检查存储权限

### 调试模式

启用调试模式获取详细日志：
```bash
./run_screenshots.sh --debug
```

查看日志文件：
```bash
tail -f screenshot_automation/screenshot_automation.log
```

## 🤝 贡献指南

### 开发环境设置

1. 导入项目到Android Studio
2. 同步Gradle依赖
3. 连接测试设备
4. 运行测试验证环境

### 代码结构

```
screenshot_automation/
├── src/main/java/com/superhexa/screenshot/
│   ├── core/           # 核心控制器
│   ├── config/         # 配置管理
│   ├── device/         # 设备管理
│   ├── navigation/     # 导航管理
│   ├── state/          # 状态管理
│   ├── traversal/      # 页面遍历
│   └── utils/          # 工具类
├── src/androidTest/    # 测试代码
├── config/             # 配置文件
└── scripts/            # 执行脚本
```

### 提交规范

- 使用清晰的提交信息
- 添加必要的测试
- 更新相关文档
- 遵循代码风格规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看[故障排除](#故障排除)部分
2. 搜索已有的[Issues](issues)
3. 创建新的Issue描述问题
4. 联系开发团队

---

**注意**: 本工具专为昆明项目设计，使用前请确保已获得必要的权限和授权。
