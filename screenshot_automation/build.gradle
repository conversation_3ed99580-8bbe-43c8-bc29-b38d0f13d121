plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.superhexa.screenshot'
    compileSdk 34

    defaultConfig {
        minSdk 21
        targetSdk 34
        
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            testCoverageEnabled true
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
        animationsDisabled = true
    }
    
    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/license.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt', 'META-INF/notice.txt', 'META-INF/ASL2.0']
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    
    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.2'
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.15.2'
    
    // 依赖注入
    implementation 'org.kodein.di:kodein-di-generic-jvm:6.5.5'
    implementation 'org.kodein.di:kodein-di-framework-android-x:6.5.5'
    
    // ARouter (如果需要)
    implementation 'com.alibaba:arouter-api:1.5.2'
    kapt 'com.alibaba:arouter-compiler:1.5.2'
    
    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.1.1'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:4.1.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    
    // Android测试依赖
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-accessibility:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-web:3.5.1'
    androidTestImplementation 'androidx.test.espresso.idling:idling-concurrent:3.5.1'
    
    // UI Automator
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'
    
    // 测试规则和运行器
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.test:core:1.5.0'
    androidTestImplementation 'androidx.test:core-ktx:1.5.0'
    
    // Fragment测试
    androidTestImplementation 'androidx.fragment:fragment-testing:1.6.2'
    
    // 权限测试
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test.ext:junit-ktx:1.1.5'
}

// 配置测试任务
tasks.withType(Test) {
    systemProperty 'robolectric.enabledSdks', '28,29,30,31,32,33,34'
    testLogging {
        events "passed", "skipped", "failed", "standardOut", "standardError"
        outputs.upToDateWhen { false }
        showStandardStreams = true
    }
}

// 自定义任务：运行截图测试
task runScreenshotTests(type: Exec) {
    group = 'screenshot'
    description = '运行截图自动化测试'
    
    // 确保应用已安装
    dependsOn 'installDebug'
    dependsOn 'installDebugAndroidTest'
    
    // 运行测试命令
    commandLine 'adb', 'shell', 'am', 'instrument', '-w', 
        '-e', 'class', 'com.superhexa.screenshot.ScreenshotAutomationTest',
        'com.superhexa.supervision.test/androidx.test.runner.AndroidJUnitRunner'
}

// 自定义任务：清理截图输出
task cleanScreenshots(type: Delete) {
    group = 'screenshot'
    description = '清理截图输出目录'
    delete fileTree(dir: './screenshots', include: '**/*')
}

// 自定义任务：生成截图报告
task generateScreenshotReport(type: Exec) {
    group = 'screenshot'
    description = '生成截图报告'
    
    dependsOn 'runScreenshotTests'
    
    doLast {
        println "截图报告已生成，请查看 ./screenshots/screenshot_report.html"
    }
}

// 自定义任务：完整的截图流程
task fullScreenshotProcess {
    group = 'screenshot'
    description = '执行完整的截图流程'
    
    dependsOn 'cleanScreenshots'
    dependsOn 'runScreenshotTests'
    dependsOn 'generateScreenshotReport'
    
    tasks.findByName('runScreenshotTests').mustRunAfter 'cleanScreenshots'
    tasks.findByName('generateScreenshotReport').mustRunAfter 'runScreenshotTests'
}

// 配置ARouter
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }
}

// 配置资源
android {
    sourceSets {
        main {
            assets.srcDirs = ['src/main/assets']
            res.srcDirs = ['src/main/res']
        }
        androidTest {
            assets.srcDirs = ['src/androidTest/assets']
        }
    }
}
