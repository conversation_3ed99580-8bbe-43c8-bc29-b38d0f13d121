#!/bin/bash

# 昆明项目自动化截图执行脚本
# 使用方法: ./run_screenshots.sh [选项]

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SCREENSHOT_OUTPUT_DIR="$SCRIPT_DIR/screenshots"
LOG_FILE="$SCRIPT_DIR/screenshot_automation.log"
CONFIG_FILE="$SCRIPT_DIR/config/screenshot_config.yaml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_debug() {
    if [[ "$DEBUG" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
昆明项目自动化截图工具

使用方法:
    $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --config FILE       指定配置文件路径 (默认: $CONFIG_FILE)
    -o, --output DIR        指定输出目录 (默认: $SCREENSHOT_OUTPUT_DIR)
    -d, --device DEVICE     指定设备ID (默认: 自动检测)
    -m, --modules MODULES   指定要截图的模块 (逗号分隔，默认: 全部)
    -s, --states STATES     指定要截图的状态 (逗号分隔，默认: 全部)
    --clean                 执行前清理输出目录
    --debug                 启用调试模式
    --dry-run              只显示将要执行的操作，不实际执行
    --report-only          只生成报告，不执行截图
    --install-only         只安装应用，不执行截图

示例:
    $0                                          # 执行完整截图流程
    $0 --clean --debug                          # 清理后执行，启用调试
    $0 -m home,login -s loading,success         # 只截图指定模块和状态
    $0 -d emulator-5554                         # 指定设备执行
    $0 --dry-run                                # 预览执行计划

EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -o|--output)
                SCREENSHOT_OUTPUT_DIR="$2"
                shift 2
                ;;
            -d|--device)
                DEVICE_ID="$2"
                shift 2
                ;;
            -m|--modules)
                MODULES="$2"
                shift 2
                ;;
            -s|--states)
                STATES="$2"
                shift 2
                ;;
            --clean)
                CLEAN_OUTPUT=true
                shift
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --report-only)
                REPORT_ONLY=true
                shift
                ;;
            --install-only)
                INSTALL_ONLY=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查环境依赖
check_dependencies() {
    log_info "检查环境依赖..."
    
    # 检查ADB
    if ! command -v adb &> /dev/null; then
        log_error "ADB未找到，请安装Android SDK并配置PATH"
        exit 1
    fi
    
    # 检查Gradle
    if ! command -v gradle &> /dev/null && [[ ! -f "$PROJECT_ROOT/gradlew" ]]; then
        log_error "Gradle未找到，请安装Gradle或使用gradlew"
        exit 1
    fi
    
    # 检查配置文件
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件未找到: $CONFIG_FILE"
        exit 1
    fi
    
    log_info "环境依赖检查通过"
}

# 检查设备连接
check_device() {
    log_info "检查设备连接..."
    
    # 获取连接的设备列表
    local devices=$(adb devices | grep -v "List of devices" | grep -E "device$|emulator$" | awk '{print $1}')
    
    if [[ -z "$devices" ]]; then
        log_error "未找到连接的设备，请确保设备已连接并启用USB调试"
        exit 1
    fi
    
    # 如果指定了设备ID，检查是否存在
    if [[ -n "$DEVICE_ID" ]]; then
        if ! echo "$devices" | grep -q "$DEVICE_ID"; then
            log_error "指定的设备未找到: $DEVICE_ID"
            log_info "可用设备: $devices"
            exit 1
        fi
        export ANDROID_SERIAL="$DEVICE_ID"
        log_info "使用指定设备: $DEVICE_ID"
    else
        # 如果只有一个设备，自动选择
        local device_count=$(echo "$devices" | wc -l)
        if [[ $device_count -eq 1 ]]; then
            DEVICE_ID="$devices"
            export ANDROID_SERIAL="$DEVICE_ID"
            log_info "自动选择设备: $DEVICE_ID"
        else
            log_error "发现多个设备，请使用 -d 参数指定设备:"
            echo "$devices"
            exit 1
        fi
    fi
    
    # 检查设备状态
    local device_state=$(adb -s "$DEVICE_ID" get-state 2>/dev/null || echo "unknown")
    if [[ "$device_state" != "device" ]]; then
        log_error "设备状态异常: $device_state"
        exit 1
    fi
    
    # 获取设备信息
    local device_model=$(adb -s "$DEVICE_ID" shell getprop ro.product.model 2>/dev/null || echo "Unknown")
    local android_version=$(adb -s "$DEVICE_ID" shell getprop ro.build.version.release 2>/dev/null || echo "Unknown")
    
    log_info "设备信息: $device_model (Android $android_version)"
}

# 准备输出目录
prepare_output_directory() {
    log_info "准备输出目录..."
    
    if [[ "$CLEAN_OUTPUT" == "true" ]] && [[ -d "$SCREENSHOT_OUTPUT_DIR" ]]; then
        log_info "清理输出目录: $SCREENSHOT_OUTPUT_DIR"
        rm -rf "$SCREENSHOT_OUTPUT_DIR"
    fi
    
    mkdir -p "$SCREENSHOT_OUTPUT_DIR"
    mkdir -p "$SCREENSHOT_OUTPUT_DIR/logs"
    
    log_info "输出目录: $SCREENSHOT_OUTPUT_DIR"
}

# 构建和安装应用
build_and_install() {
    log_info "构建和安装应用..."
    
    cd "$PROJECT_ROOT"
    
    # 构建应用
    if [[ -f "./gradlew" ]]; then
        GRADLE_CMD="./gradlew"
    else
        GRADLE_CMD="gradle"
    fi
    
    log_info "构建应用..."
    if [[ "$DRY_RUN" != "true" ]]; then
        $GRADLE_CMD assembleDebug assembleDebugAndroidTest
        
        # 安装应用
        log_info "安装应用..."
        $GRADLE_CMD installDebug installDebugAndroidTest
    else
        log_info "[DRY RUN] 将执行: $GRADLE_CMD assembleDebug assembleDebugAndroidTest"
        log_info "[DRY RUN] 将执行: $GRADLE_CMD installDebug installDebugAndroidTest"
    fi
}

# 配置设备环境
configure_device() {
    log_info "配置设备环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将配置设备环境"
        return
    fi
    
    # 唤醒设备
    adb -s "$DEVICE_ID" shell input keyevent KEYCODE_WAKEUP
    
    # 解锁屏幕（简单的向上滑动）
    adb -s "$DEVICE_ID" shell input swipe 500 1000 500 300
    
    # 禁用动画以提高截图稳定性
    adb -s "$DEVICE_ID" shell settings put global window_animation_scale 0
    adb -s "$DEVICE_ID" shell settings put global transition_animation_scale 0
    adb -s "$DEVICE_ID" shell settings put global animator_duration_scale 0
    
    # 设置屏幕常亮
    adb -s "$DEVICE_ID" shell settings put system screen_off_timeout 2147483647
    
    # 设置最高亮度
    adb -s "$DEVICE_ID" shell settings put system screen_brightness 255
    
    log_info "设备环境配置完成"
}

# 执行截图测试
run_screenshot_tests() {
    log_info "执行截图测试..."
    
    cd "$PROJECT_ROOT"
    
    # 构建测试命令
    local test_cmd="$GRADLE_CMD connectedAndroidTest"
    
    # 添加系统属性
    test_cmd="$test_cmd -Dscreenshot.output.dir=$SCREENSHOT_OUTPUT_DIR"
    test_cmd="$test_cmd -Dscreenshot.config.file=$CONFIG_FILE"
    
    if [[ -n "$MODULES" ]]; then
        test_cmd="$test_cmd -Dscreenshot.modules=$MODULES"
    fi
    
    if [[ -n "$STATES" ]]; then
        test_cmd="$test_cmd -Dscreenshot.states=$STATES"
    fi
    
    if [[ "$DEBUG" == "true" ]]; then
        test_cmd="$test_cmd -Dscreenshot.debug=true"
    fi
    
    # 执行测试
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将执行: $test_cmd"
    else
        log_info "执行命令: $test_cmd"
        eval "$test_cmd" 2>&1 | tee -a "$LOG_FILE"
        
        local exit_code=${PIPESTATUS[0]}
        if [[ $exit_code -eq 0 ]]; then
            log_info "截图测试执行成功"
        else
            log_error "截图测试执行失败，退出码: $exit_code"
            return $exit_code
        fi
    fi
}

# 生成报告
generate_report() {
    log_info "生成截图报告..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将生成截图报告"
        return
    fi
    
    # 统计截图数量
    local total_screenshots=$(find "$SCREENSHOT_OUTPUT_DIR" -name "*.png" | wc -l)
    local total_modules=$(find "$SCREENSHOT_OUTPUT_DIR" -mindepth 1 -maxdepth 1 -type d | wc -l)
    
    # 生成简单的文本报告
    local report_file="$SCREENSHOT_OUTPUT_DIR/screenshot_summary.txt"
    cat > "$report_file" << EOF
昆明项目截图自动化报告
========================

执行时间: $(date)
设备信息: $DEVICE_ID
输出目录: $SCREENSHOT_OUTPUT_DIR

统计信息:
- 总截图数: $total_screenshots
- 模块数: $total_modules

详细信息请查看各模块目录下的截图文件。

EOF
    
    log_info "报告已生成: $report_file"
    log_info "总截图数: $total_screenshots"
    log_info "模块数: $total_modules"
}

# 恢复设备环境
restore_device() {
    log_info "恢复设备环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将恢复设备环境"
        return
    fi
    
    # 恢复动画
    adb -s "$DEVICE_ID" shell settings put global window_animation_scale 1
    adb -s "$DEVICE_ID" shell settings put global transition_animation_scale 1
    adb -s "$DEVICE_ID" shell settings put global animator_duration_scale 1
    
    # 恢复屏幕超时
    adb -s "$DEVICE_ID" shell settings put system screen_off_timeout 30000
    
    log_info "设备环境已恢复"
}

# 主函数
main() {
    log_info "开始执行昆明项目自动化截图"
    log_info "脚本目录: $SCRIPT_DIR"
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 解析参数
    parse_arguments "$@"
    
    # 如果是只生成报告模式
    if [[ "$REPORT_ONLY" == "true" ]]; then
        generate_report
        exit 0
    fi
    
    # 检查环境
    check_dependencies
    check_device
    prepare_output_directory
    
    # 如果是只安装模式
    if [[ "$INSTALL_ONLY" == "true" ]]; then
        build_and_install
        exit 0
    fi
    
    # 执行完整流程
    local start_time=$(date +%s)
    
    build_and_install
    configure_device
    
    # 执行截图测试
    if run_screenshot_tests; then
        generate_report
        log_info "截图自动化执行成功"
    else
        log_error "截图自动化执行失败"
        exit 1
    fi
    
    # 恢复环境
    restore_device
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "总耗时: ${duration}秒"
    log_info "截图输出目录: $SCREENSHOT_OUTPUT_DIR"
    log_info "日志文件: $LOG_FILE"
}

# 信号处理
trap 'log_error "脚本被中断"; restore_device; exit 1' INT TERM

# 执行主函数
main "$@"
