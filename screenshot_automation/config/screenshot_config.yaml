# 昆明项目自动化截图配置文件
# 配置版本
version: "1.0"

# 项目基本信息
project:
  name: "昆明项目"
  package_name: "com.superhexa.supervision"
  main_activity: "com.superhexa.supervision.NavHostActivity"

# 设备配置
devices:
  # 手机设备配置
  phone:
    - name: "小屏手机"
      width: 360
      height: 640
      density: 2.0
    - name: "中屏手机"
      width: 414
      height: 896
      density: 3.0
    - name: "大屏手机"
      width: 428
      height: 926
      density: 3.0
  
  # 平板设备配置
  tablet:
    - name: "小平板"
      width: 768
      height: 1024
      density: 2.0
    - name: "大平板"
      width: 1024
      height: 1366
      density: 2.0

# 截图输出配置
output:
  base_path: "../docs/screenshots"
  format: "png"
  quality: 100
  naming_pattern: "{module}_{page}_{state}_{device}_{timestamp}"
  
# 功能模块配置
modules:
  # 主应用模块
  app:
    name: "主应用"
    fragments:
      - name: "SplashFragment"
        route: "/app/SplashFragment"
        states: ["loading", "success"]
      - name: "MainFragment"
        route: "/app/MainFragment"
        states: ["empty", "device_connected", "multiple_devices"]
  
  # 首页模块
  home:
    name: "首页"
    fragments:
      - name: "HomeFragment"
        route: "/home/<USER>"
        states: ["loading", "empty", "with_templates", "downloading"]
      - name: "DeviceAddFragment"
        route: "/home/<USER>"
        states: ["searching", "found_one", "found_multiple", "bind_success", "bind_failed"]
      - name: "MaterialFragment"
        route: "/home/<USER>"
        states: ["loading", "empty", "with_content"]
      - name: "TemplateListFragment"
        route: "/home/<USER>"
        states: ["loading", "empty", "with_templates"]
  
  # 登录模块
  login:
    name: "登录"
    fragments:
      - name: "LoginFragment"
        route: "/login/LoginFragment"
        states: ["initial", "phone_input", "code_input", "logging_in", "error"]
      - name: "LoginAccessFragment"
        route: "/login/LoginAccessFragment"
        states: ["initial", "logged_in"]
      - name: "ForgotPasswordFragment"
        route: "/login/ForgotPasswordFragment"
        states: ["initial", "email_input", "sending", "sent"]
  
  # 设备模块
  device:
    name: "设备管理"
    fragments:
      - name: "DeviceListFragment"
        route: "/device/DeviceListFragment"
        states: ["loading", "empty", "with_devices", "connecting"]
  
  # 音频眼镜模块
  audioglasses:
    name: "音频眼镜"
    fragments:
      - name: "SSHomeFragment"
        route: "/audioglasses/SSHomeFragment"
        states: ["connected", "disconnected", "recording", "playing"]
      - name: "SS2HomeFragment"
        route: "/audioglasses/SS2HomeFragment"
        states: ["connected", "disconnected", "recording", "playing"]
      - name: "SSSHomeFragment"
        route: "/audioglasses/SSSHomeFragment"
        states: ["connected", "disconnected", "recording", "playing"]
  
  # 小爱同学模块
  xiaoai:
    name: "小爱同学"
    fragments:
      - name: "XiaoAiFragment"
        route: "/xiaoai/XiaoAiFragment"
        states: ["listening", "processing", "responding", "idle"]
  
  # 视频编辑模块
  videoeditor:
    name: "视频编辑"
    fragments:
      - name: "VideoEditorFragment"
        route: "/videoeditor/VideoEditorFragment"
        states: ["loading", "editing", "preview", "exporting"]
      - name: "FileExplorerFragment"
        route: "/videoeditor/FileExplorerFragment"
        states: ["loading", "empty", "with_files"]
  
  # 用户资料模块
  profile:
    name: "用户资料"
    fragments:
      - name: "ProfileFragment"
        route: "/profile/ProfileFragment"
        states: ["loading", "logged_in", "logged_out"]
      - name: "SettingsFragment"
        route: "/profile/SettingsFragment"
        states: ["default"]

# 用户权限配置
user_permissions:
  - name: "未登录用户"
    login_required: false
    features: ["device_add", "basic_functions"]
  - name: "普通用户"
    login_required: true
    features: ["all_basic", "templates", "recording"]
  - name: "VIP用户"
    login_required: true
    features: ["all_features", "premium_templates", "advanced_editing"]

# 网络状态配置
network_states:
  - name: "在线"
    connected: true
    speed: "fast"
  - name: "离线"
    connected: false
  - name: "慢速网络"
    connected: true
    speed: "slow"

# 数据状态配置
data_states:
  - name: "空数据"
    description: "无任何数据的状态"
  - name: "少量数据"
    description: "1-3条数据的状态"
  - name: "正常数据"
    description: "正常数量数据的状态"
  - name: "大量数据"
    description: "大量数据的状态"

# 截图执行配置
execution:
  # 每个页面的等待时间（毫秒）
  page_load_timeout: 5000
  # 状态切换等待时间（毫秒）
  state_change_timeout: 3000
  # 截图间隔时间（毫秒）
  screenshot_interval: 1000
  # 失败重试次数
  retry_count: 3
  # 并发执行数量
  parallel_count: 1

# 过滤配置
filters:
  # 跳过的页面
  skip_pages: []
  # 跳过的状态
  skip_states: []
  # 只截图指定模块
  include_modules: []
  # 排除指定模块
  exclude_modules: []
