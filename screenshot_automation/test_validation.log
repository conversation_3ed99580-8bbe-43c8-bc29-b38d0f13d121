[0;32m[INFO][0m 2025-08-19 18:06:53 开始环境验证测试
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: Java环境检查
openjdk version "17.0.14" 2025-01-21
OpenJDK Runtime Environment JBR-17.0.14+1-1367.22-nomod (build 17.0.14+1-b1367.22)
OpenJDK 64-Bit Server VM JBR-17.0.14+1-1367.22-nomod (build 17.0.14+1-b1367.22, mixed mode, sharing)
[0;32m[SUCCESS][0m 2025-08-19 18:06:53 测试通过: Java环境检查
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: Android SDK检查
[0;32m[SUCCESS][0m 2025-08-19 18:06:53 测试通过: Android SDK检查
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: ADB工具检查
Android Debug Bridge version 1.0.41
Version 35.0.2-12147458
Installed as /Users/<USER>/Library/Android/sdk/platform-tools/adb
Running on Darwin 24.3.0 (arm64)
[0;32m[SUCCESS][0m 2025-08-19 18:06:53 测试通过: ADB工具检查
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: Gradle工具检查
[0;32m[SUCCESS][0m 2025-08-19 18:06:53 测试通过: Gradle工具检查
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: 配置文件检查
[0;32m[SUCCESS][0m 2025-08-19 18:06:53 测试通过: 配置文件检查
[0;32m[INFO][0m 2025-08-19 18:06:53 环境验证测试完成
[0;32m[INFO][0m 2025-08-19 18:06:53 开始配置文件验证测试
[0;32m[INFO][0m 2025-08-19 18:06:53 执行测试: YAML格式验证
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: YAML格式验证
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 项目配置验证
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 项目配置验证
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 设备配置验证
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 设备配置验证
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 输出配置验证
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 输出配置验证
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 模块配置验证
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 模块配置验证
[0;32m[INFO][0m 2025-08-19 18:06:54 配置文件验证测试完成
[0;32m[INFO][0m 2025-08-19 18:06:54 开始项目结构验证测试
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 核心控制器文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 核心控制器文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 配置管理文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 配置管理文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 导航管理文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 导航管理文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 状态管理文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 状态管理文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 设备管理文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 设备管理文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 主测试文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 主测试文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 构建配置文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 构建配置文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 执行脚本文件
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 执行脚本文件
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: README文档
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: README文档
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: 使用指南文档
[0;32m[SUCCESS][0m 2025-08-19 18:06:54 测试通过: 使用指南文档
[0;32m[INFO][0m 2025-08-19 18:06:54 项目结构验证测试完成
[0;32m[INFO][0m 2025-08-19 18:06:54 开始代码语法验证测试
[1;33m[WARN][0m 2025-08-19 18:06:54 kotlinc未找到，跳过Kotlin语法检查
[0;32m[INFO][0m 2025-08-19 18:06:54 执行测试: Gradle语法检查
To honour the JVM settings for this build a single-use Daemon process will be forked. For more on this, please refer to https://docs.gradle.org/8.7/userguide/gradle_daemon.html#sec:disabling_the_daemon in the Gradle documentation.
Daemon will be stopped at the end of the build 
Configuration on demand is an incubating feature.

FAILURE: Build failed with an exception.

* What went wrong:
Cannot locate tasks that match ':screenshot_automation:help' as project 'screenshot_automation' not found in root project 'Kunming'.

* Try:
> Run gradlew projects to get a list of available projects.
> For more on name expansion, please refer to https://docs.gradle.org/8.7/userguide/command_line_interface.html#sec:name_abbreviation in the Gradle documentation.
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 2s
[0;31m[ERROR][0m 2025-08-19 18:06:57 测试失败: Gradle语法检查
