#!/bin/bash

# 简化的昆明项目截图脚本
# 直接使用ADB进行截图，不依赖Gradle构建

set -e

# 配置
PACKAGE_NAME="com.superhexa.supervision"
OUTPUT_DIR="$(pwd)/../docs/screenshots"
DEVICE_ID="RXD0221730002851"
LOG_FILE="simple_screenshot.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 截图计数
SCREENSHOT_COUNT=0

# 创建输出目录
create_output_dirs() {
    log_info "创建输出目录结构"
    
    mkdir -p "$OUTPUT_DIR"
    mkdir -p "$OUTPUT_DIR/app"
    mkdir -p "$OUTPUT_DIR/home"
    mkdir -p "$OUTPUT_DIR/login"
    mkdir -p "$OUTPUT_DIR/device"
    mkdir -p "$OUTPUT_DIR/profile"
    mkdir -p "$OUTPUT_DIR/audioglasses"
    mkdir -p "$OUTPUT_DIR/videoeditor"
    mkdir -p "$OUTPUT_DIR/xiaoai"
    mkdir -p "$OUTPUT_DIR/responsive"
    mkdir -p "$OUTPUT_DIR/metadata"
    
    log_info "输出目录创建完成: $OUTPUT_DIR"
}

# 配置设备环境
configure_device() {
    log_info "配置设备环境"
    
    # 唤醒设备
    adb -s "$DEVICE_ID" shell input keyevent KEYCODE_WAKEUP
    sleep 1
    
    # 解锁屏幕
    adb -s "$DEVICE_ID" shell input swipe 500 1000 500 300
    sleep 1
    
    # 禁用动画
    adb -s "$DEVICE_ID" shell settings put global window_animation_scale 0
    adb -s "$DEVICE_ID" shell settings put global transition_animation_scale 0
    adb -s "$DEVICE_ID" shell settings put global animator_duration_scale 0
    
    # 设置屏幕常亮
    adb -s "$DEVICE_ID" shell settings put system screen_off_timeout 2147483647
    
    # 设置最高亮度
    adb -s "$DEVICE_ID" shell settings put system screen_brightness 255
    
    log_info "设备环境配置完成"
}

# 启动应用
launch_app() {
    log_info "启动应用: $PACKAGE_NAME"
    
    # 强制停止应用
    adb -s "$DEVICE_ID" shell am force-stop "$PACKAGE_NAME"
    sleep 2
    
    # 启动应用
    adb -s "$DEVICE_ID" shell monkey -p "$PACKAGE_NAME" -c android.intent.category.LAUNCHER 1
    sleep 5
    
    log_info "应用启动完成"
}

# 执行截图
take_screenshot() {
    local module="$1"
    local page="$2"
    local state="$3"
    local description="$4"
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local filename="${module}_${page}_${state}_phone_${timestamp}.png"
    local filepath="$OUTPUT_DIR/$module/$filename"
    
    log_info "截图: $description"
    
    # 等待界面稳定
    sleep 2
    
    # 执行截图
    if adb -s "$DEVICE_ID" shell screencap -p "/sdcard/$filename"; then
        # 拉取截图到本地
        if adb -s "$DEVICE_ID" pull "/sdcard/$filename" "$filepath"; then
            # 删除设备上的临时文件
            adb -s "$DEVICE_ID" shell rm "/sdcard/$filename"
            
            # 生成元数据
            generate_metadata "$filepath" "$module" "$page" "$state" "$description"
            
            SCREENSHOT_COUNT=$((SCREENSHOT_COUNT + 1))
            log_info "截图成功: $filename"
            return 0
        else
            log_error "截图拉取失败: $filename"
            return 1
        fi
    else
        log_error "截图执行失败: $filename"
        return 1
    fi
}

# 生成元数据
generate_metadata() {
    local filepath="$1"
    local module="$2"
    local page="$3"
    local state="$4"
    local description="$5"
    
    local filename=$(basename "$filepath")
    local filesize=$(stat -f%z "$filepath" 2>/dev/null || echo "0")
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    local metadata_file="$OUTPUT_DIR/metadata/${filename%.png}.json"
    
    cat > "$metadata_file" << EOF
{
  "screenshot_file": "$filename",
  "module": "$module",
  "page": "$page",
  "state": "$state",
  "description": "$description",
  "device": "OCE-AN50",
  "device_width": 1080,
  "device_height": 2340,
  "device_density": 3.0,
  "timestamp": "$timestamp",
  "file_size": $filesize,
  "file_path": "$filepath"
}
EOF
}

# 点击元素（通过坐标）
click_element() {
    local x="$1"
    local y="$2"
    local description="$3"
    
    log_info "点击元素: $description ($x, $y)"
    adb -s "$DEVICE_ID" shell input tap "$x" "$y"
    sleep 2
}

# 点击文本元素
click_text() {
    local text="$1"
    local description="$2"
    
    log_info "点击文本: $text ($description)"
    
    # 使用UI Automator查找并点击文本
    adb -s "$DEVICE_ID" shell uiautomator runtest /system/framework/uiautomator.jar /system/framework/android.test.runner.jar -c "android.support.test.uiautomator.UiAutomatorTestCase" -e text "$text" || true
    sleep 2
}

# 返回操作
go_back() {
    log_info "执行返回操作"
    adb -s "$DEVICE_ID" shell input keyevent KEYCODE_BACK
    sleep 2
}

# 返回首页
go_home() {
    log_info "返回应用首页"
    adb -s "$DEVICE_ID" shell am start -n "$PACKAGE_NAME/.NavHostActivity" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER
    sleep 3
}

# 截图主要界面
capture_main_screens() {
    log_info "开始截图主要界面"
    
    # 1. 启动页面
    take_screenshot "app" "splash" "loading" "应用启动页面"
    
    # 2. 主页面
    go_home
    take_screenshot "home" "main" "default" "应用主页面"
    
    # 3. 尝试截图不同的底部导航页面
    # 首页
    click_element 200 2200 "首页标签"
    take_screenshot "home" "home" "default" "首页界面"
    
    # 设备页面
    click_element 400 2200 "设备标签"
    take_screenshot "device" "device_list" "default" "设备列表页面"
    
    # 个人中心
    click_element 800 2200 "个人中心标签"
    take_screenshot "profile" "profile" "default" "个人中心页面"
    
    # 4. 尝试进入设置页面
    click_element 900 200 "设置按钮"
    take_screenshot "profile" "settings" "default" "设置页面"
    
    go_back
    
    # 5. 尝试截图登录相关页面
    click_element 540 1000 "登录按钮"
    take_screenshot "login" "login" "default" "登录页面"
    
    go_back
    
    log_info "主要界面截图完成"
}

# 截图不同状态
capture_different_states() {
    log_info "开始截图不同状态"
    
    go_home
    
    # 模拟加载状态 - 下拉刷新
    adb -s "$DEVICE_ID" shell input swipe 540 500 540 1500 500
    sleep 1
    take_screenshot "home" "home" "loading" "首页加载状态"
    sleep 3
    
    # 正常状态
    take_screenshot "home" "home" "success" "首页正常状态"
    
    # 尝试进入设备添加页面
    click_element 540 1200 "添加设备按钮"
    take_screenshot "device" "device_add" "searching" "设备搜索页面"
    
    go_back
    
    log_info "不同状态截图完成"
}

# 截图响应式设计（模拟不同方向）
capture_responsive_design() {
    log_info "开始截图响应式设计"
    
    go_home
    
    # 竖屏模式
    take_screenshot "responsive" "home" "portrait" "首页竖屏模式"
    
    # 横屏模式
    adb -s "$DEVICE_ID" shell settings put system user_rotation 1
    sleep 3
    take_screenshot "responsive" "home" "landscape" "首页横屏模式"
    
    # 恢复竖屏
    adb -s "$DEVICE_ID" shell settings put system user_rotation 0
    sleep 3
    
    log_info "响应式设计截图完成"
}

# 生成HTML报告
generate_html_report() {
    log_info "生成HTML报告"
    
    local report_file="$OUTPUT_DIR/screenshot_report.html"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>昆明项目截图报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; flex: 1; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 24px; font-weight: bold; color: #2196F3; }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .screenshot-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .screenshot-image { width: 100%; height: 400px; object-fit: contain; background: #f0f0f0; }
        .screenshot-info { padding: 15px; }
        .screenshot-title { font-weight: bold; margin-bottom: 10px; color: #333; }
        .screenshot-meta { font-size: 12px; color: #666; line-height: 1.4; }
        .module-section { margin-bottom: 30px; }
        .module-title { font-size: 20px; font-weight: bold; margin-bottom: 15px; padding: 10px; background: #2196F3; color: white; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>昆明项目自动化截图报告</h1>
        <p>生成时间：$timestamp</p>
        <p>设备信息：OCE-AN50 (Android 12)</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">$SCREENSHOT_COUNT</div>
            <div>总截图数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$(find "$OUTPUT_DIR" -mindepth 1 -maxdepth 1 -type d | wc -l)</div>
            <div>模块数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$(find "$OUTPUT_DIR" -name "*.png" | wc -l)</div>
            <div>截图文件数</div>
        </div>
    </div>
    
    <div class="module-section">
        <div class="module-title">截图预览</div>
        <div class="screenshot-grid">
EOF

    # 添加截图卡片
    find "$OUTPUT_DIR" -name "*.png" | sort | while read -r screenshot; do
        local filename=$(basename "$screenshot")
        local relative_path=$(realpath --relative-to="$OUTPUT_DIR" "$screenshot")
        
        cat >> "$report_file" << EOF
            <div class="screenshot-card">
                <img src="$relative_path" alt="$filename" class="screenshot-image" />
                <div class="screenshot-info">
                    <div class="screenshot-title">$filename</div>
                    <div class="screenshot-meta">
                        文件大小：$(stat -f%z "$screenshot" 2>/dev/null || echo "未知")字节<br>
                        修改时间：$(stat -f%Sm "$screenshot" 2>/dev/null || echo "未知")
                    </div>
                </div>
            </div>
EOF
    done
    
    cat >> "$report_file" << EOF
        </div>
    </div>
</body>
</html>
EOF
    
    log_info "HTML报告生成完成: $report_file"
}

# 恢复设备环境
restore_device() {
    log_info "恢复设备环境"
    
    # 恢复动画
    adb -s "$DEVICE_ID" shell settings put global window_animation_scale 1
    adb -s "$DEVICE_ID" shell settings put global transition_animation_scale 1
    adb -s "$DEVICE_ID" shell settings put global animator_duration_scale 1
    
    # 恢复屏幕超时
    adb -s "$DEVICE_ID" shell settings put system screen_off_timeout 30000
    
    log_info "设备环境已恢复"
}

# 主函数
main() {
    log_info "开始执行昆明项目简化截图流程"
    
    # 清空日志
    > "$LOG_FILE"
    
    # 执行截图流程
    create_output_dirs
    configure_device
    launch_app
    
    capture_main_screens
    capture_different_states
    capture_responsive_design
    
    generate_html_report
    restore_device
    
    # 输出结果
    echo
    echo "=================================="
    echo "截图任务完成"
    echo "=================================="
    echo "总截图数: $SCREENSHOT_COUNT"
    echo "输出目录: $OUTPUT_DIR"
    echo "HTML报告: $OUTPUT_DIR/screenshot_report.html"
    echo
    
    log_info "截图任务执行完成"
}

# 执行主函数
main "$@"
