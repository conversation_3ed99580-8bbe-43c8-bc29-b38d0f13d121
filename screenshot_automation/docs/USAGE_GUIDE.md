# 昆明项目自动化截图使用指南

本指南详细介绍如何使用昆明项目自动化截图工具，包括配置、执行、定制和故障排除。

## 📋 目录

1. [环境准备](#环境准备)
2. [配置详解](#配置详解)
3. [执行方式](#执行方式)
4. [定制开发](#定制开发)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)

## 🔧 环境准备

### 1. 开发环境要求

```bash
# 检查Java版本 (需要JDK 8+)
java -version

# 检查Android SDK
echo $ANDROID_HOME

# 检查ADB
adb version

# 检查Gradle
./gradlew --version
```

### 2. 设备准备

#### Android设备设置
1. **启用开发者选项**
   - 设置 → 关于手机 → 连续点击版本号7次

2. **启用USB调试**
   - 设置 → 开发者选项 → USB调试

3. **启用模拟点击权限**
   - 设置 → 开发者选项 → 指针位置
   - 设置 → 开发者选项 → 显示触摸操作

4. **禁用自动锁屏**
   - 设置 → 显示 → 休眠 → 永不

#### 模拟器设置
```bash
# 创建AVD
avd create -n screenshot_test -k "system-images;android-30;google_apis;x86_64"

# 启动模拟器
emulator -avd screenshot_test -no-snapshot-load
```

### 3. 项目集成

#### 添加到现有项目
```gradle
// 在根目录的settings.gradle中添加
include ':screenshot_automation'

// 在app模块的build.gradle中添加依赖
androidTestImplementation project(':screenshot_automation')
```

#### 权限配置
```xml
<!-- 在AndroidManifest.xml中添加 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
```

## ⚙️ 配置详解

### 1. 基础配置

#### 项目信息配置
```yaml
project:
  name: "昆明项目"
  package_name: "com.superhexa.supervision"  # 必须与实际包名一致
  main_activity: "com.superhexa.supervision.NavHostActivity"  # 主Activity完整类名
```

#### 输出配置
```yaml
output:
  base_path: "./screenshots"          # 输出根目录
  format: "png"                       # 图片格式: png, jpg
  quality: 100                        # 图片质量 (1-100)
  naming_pattern: "{module}_{page}_{state}_{device}_{timestamp}"  # 命名规则
```

### 2. 设备配置

#### 手机设备配置
```yaml
devices:
  phone:
    - name: "iPhone SE"               # 设备名称
      width: 375                      # 屏幕宽度
      height: 667                     # 屏幕高度
      density: 2.0                    # 屏幕密度
    - name: "iPhone 12"
      width: 390
      height: 844
      density: 3.0
    - name: "Pixel 5"
      width: 393
      height: 851
      density: 2.75
```

#### 平板设备配置
```yaml
  tablet:
    - name: "iPad"
      width: 768
      height: 1024
      density: 2.0
    - name: "iPad Pro"
      width: 1024
      height: 1366
      density: 2.0
```

### 3. 模块配置

#### 完整模块配置示例
```yaml
modules:
  home:
    name: "首页模块"
    fragments:
      - name: "HomeFragment"
        route: "/home/<USER>"
        states: ["loading", "empty", "with_data", "error"]
      - name: "DeviceAddFragment"
        route: "/home/<USER>"
        states: ["searching", "found_one", "found_multiple", "bind_success", "bind_failed"]
  
  login:
    name: "登录模块"
    fragments:
      - name: "LoginFragment"
        route: "/login/LoginFragment"
        states: ["initial", "phone_input", "code_input", "logging_in", "error"]
```

#### 状态定义说明
- `loading`: 加载中状态
- `empty`: 空数据状态
- `error`: 错误状态
- `success`: 成功状态
- `offline`: 离线状态
- `logged_out`: 未登录状态

### 4. 执行配置

```yaml
execution:
  page_load_timeout: 5000           # 页面加载超时(ms)
  state_change_timeout: 3000        # 状态切换超时(ms)
  screenshot_interval: 1000         # 截图间隔(ms)
  retry_count: 3                    # 失败重试次数
  parallel_count: 1                 # 并发执行数量
```

### 5. 过滤配置

```yaml
filters:
  skip_pages: ["DebugFragment"]     # 跳过的页面
  skip_states: ["debug"]            # 跳过的状态
  include_modules: ["home", "login"] # 只包含的模块
  exclude_modules: ["debug"]        # 排除的模块
```

## 🚀 执行方式

### 1. 命令行执行

#### 基本执行
```bash
# 完整截图流程
./run_screenshots.sh

# 清理后执行
./run_screenshots.sh --clean

# 调试模式执行
./run_screenshots.sh --debug
```

#### 选择性执行
```bash
# 只截图指定模块
./run_screenshots.sh -m home,login

# 只截图指定状态
./run_screenshots.sh -s loading,success,error

# 指定输出目录
./run_screenshots.sh -o /path/to/output

# 指定配置文件
./run_screenshots.sh -c /path/to/config.yaml
```

#### 设备相关
```bash
# 指定设备
./run_screenshots.sh -d emulator-5554

# 列出可用设备
adb devices
```

### 2. Gradle任务执行

```bash
# 完整流程
./gradlew :screenshot_automation:fullScreenshotProcess

# 只运行测试
./gradlew :screenshot_automation:runScreenshotTests

# 清理输出
./gradlew :screenshot_automation:cleanScreenshots

# 生成报告
./gradlew :screenshot_automation:generateScreenshotReport
```

### 3. Android Studio执行

1. 打开Android Studio
2. 选择 `screenshot_automation` 模块
3. 右键点击 `ScreenshotAutomationTest`
4. 选择 "Run 'ScreenshotAutomationTest'"

### 4. CI/CD集成

#### GitHub Actions示例
```yaml
name: Screenshot Automation

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  screenshot:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11'
        distribution: 'adopt'
    
    - name: Setup Android SDK
      uses: android-actions/setup-android@v2
    
    - name: Start emulator
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 30
        script: |
          cd screenshot_automation
          ./run_screenshots.sh --clean
    
    - name: Upload screenshots
      uses: actions/upload-artifact@v2
      with:
        name: screenshots
        path: screenshot_automation/screenshots/
```

## 🛠️ 定制开发

### 1. 自定义状态策略

```kotlin
// 创建自定义状态策略
class CustomLoadingStateStrategy : StateStrategy {
    override fun applyState(
        context: Context, 
        uiDevice: UiDevice, 
        stateManager: StateManager
    ): StateSetResult {
        return try {
            // 1. 触发特定的加载操作
            triggerCustomLoading(uiDevice)
            
            // 2. 等待加载指示器出现
            waitForLoadingIndicator(uiDevice)
            
            StateSetResult.success("自定义加载状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("设置失败: ${e.message}")
        }
    }
    
    private fun triggerCustomLoading(uiDevice: UiDevice) {
        // 实现具体的加载触发逻辑
        uiDevice.findObject(By.text("刷新")).click()
    }
    
    private fun waitForLoadingIndicator(uiDevice: UiDevice) {
        uiDevice.wait(Until.hasObject(By.res("loading_indicator")), 5000)
    }
}

// 注册自定义策略
val customStrategies = mapOf(
    "custom_loading" to CustomLoadingStateStrategy()
)
```

### 2. 扩展页面发现

```kotlin
class CustomPageTraversal : PageTraversal {
    
    override fun discoverCustomPages(): List<PageTarget> {
        val customPages = mutableListOf<PageTarget>()
        
        // 发现动态生成的页面
        val dynamicPages = discoverDynamicPages()
        customPages.addAll(dynamicPages)
        
        // 发现深层链接页面
        val deepLinkPages = discoverDeepLinkPages()
        customPages.addAll(deepLinkPages)
        
        return customPages
    }
    
    private fun discoverDynamicPages(): List<PageTarget> {
        // 实现动态页面发现逻辑
        return emptyList()
    }
    
    private fun discoverDeepLinkPages(): List<PageTarget> {
        // 实现深层链接页面发现逻辑
        return emptyList()
    }
}
```

### 3. 自定义截图处理

```kotlin
class CustomScreenshotProcessor {
    
    fun processScreenshot(screenshotFile: File): ProcessResult {
        return try {
            // 1. 图片压缩
            compressImage(screenshotFile)
            
            // 2. 添加水印
            addWatermark(screenshotFile)
            
            // 3. 生成缩略图
            generateThumbnail(screenshotFile)
            
            ProcessResult.success()
        } catch (e: Exception) {
            ProcessResult.failure(e.message ?: "处理失败")
        }
    }
    
    private fun compressImage(file: File) {
        // 实现图片压缩逻辑
    }
    
    private fun addWatermark(file: File) {
        // 实现水印添加逻辑
    }
    
    private fun generateThumbnail(file: File) {
        // 实现缩略图生成逻辑
    }
}
```

### 4. 自定义报告生成

```kotlin
class CustomReportGenerator {
    
    fun generateCustomReport(screenshots: List<ScreenshotInfo>): File {
        val reportData = analyzeScreenshots(screenshots)
        
        return when (reportData.format) {
            "html" -> generateHtmlReport(reportData)
            "pdf" -> generatePdfReport(reportData)
            "excel" -> generateExcelReport(reportData)
            else -> generateJsonReport(reportData)
        }
    }
    
    private fun analyzeScreenshots(screenshots: List<ScreenshotInfo>): ReportData {
        // 分析截图数据
        return ReportData(
            totalCount = screenshots.size,
            moduleStats = screenshots.groupBy { it.module },
            stateStats = screenshots.groupBy { it.state },
            deviceStats = screenshots.groupBy { it.device }
        )
    }
}
```

## 💡 最佳实践

### 1. 配置优化

#### 设备配置建议
```yaml
# 推荐的设备配置覆盖主流尺寸
devices:
  phone:
    - name: "小屏手机"      # 覆盖小屏设备
      width: 360
      height: 640
      density: 2.0
    - name: "标准手机"      # 覆盖主流设备
      width: 414
      height: 896
      density: 3.0
    - name: "大屏手机"      # 覆盖大屏设备
      width: 428
      height: 926
      density: 3.0
```

#### 状态配置建议
```yaml
# 推荐的通用状态配置
common_states: &common_states
  - "loading"      # 加载状态
  - "empty"        # 空数据状态
  - "success"      # 成功状态
  - "error"        # 错误状态

# 在Fragment配置中引用
fragments:
  - name: "HomeFragment"
    route: "/home/<USER>"
    states: *common_states
```

### 2. 执行优化

#### 批量执行策略
```bash
# 分模块执行，避免一次性执行时间过长
./run_screenshots.sh -m home --clean
./run_screenshots.sh -m login
./run_screenshots.sh -m profile

# 分状态执行，优先执行重要状态
./run_screenshots.sh -s loading,success
./run_screenshots.sh -s error,empty
```

#### 并发执行配置
```yaml
execution:
  parallel_count: 2        # 适度并发，避免设备负载过高
  retry_count: 3           # 充分重试，提高成功率
  screenshot_interval: 1500 # 适当间隔，确保状态稳定
```

### 3. 质量保证

#### 截图质量检查
```kotlin
// 实现截图质量检查
class ScreenshotQualityChecker {
    
    fun checkQuality(screenshotFile: File): QualityResult {
        val bitmap = BitmapFactory.decodeFile(screenshotFile.absolutePath)
        
        return QualityResult(
            isBlank = isBlankScreen(bitmap),
            hasLoadingIndicator = hasLoadingIndicator(bitmap),
            isPartiallyLoaded = isPartiallyLoaded(bitmap),
            brightness = calculateBrightness(bitmap)
        )
    }
}
```

#### 自动重试机制
```yaml
execution:
  retry_count: 3
  retry_delay: 2000        # 重试间隔
  max_retry_time: 30000    # 最大重试时间
```

### 4. 维护建议

#### 定期更新配置
- 新增页面时及时更新配置
- 定期检查路由是否有效
- 根据UI变化调整状态定义

#### 监控执行结果
- 定期检查截图成功率
- 分析失败原因并优化
- 监控执行时间趋势

## 🔍 故障排除

### 1. 常见错误及解决方案

#### 设备连接问题
```bash
# 错误: device not found
# 解决方案:
adb kill-server
adb start-server
adb devices

# 错误: device offline
# 解决方案:
adb disconnect
adb connect <device_ip>
```

#### 权限问题
```bash
# 错误: Permission denied
# 解决方案:
adb shell pm grant com.superhexa.supervision android.permission.WRITE_EXTERNAL_STORAGE
adb shell pm grant com.superhexa.supervision android.permission.SYSTEM_ALERT_WINDOW
```

#### 应用安装问题
```bash
# 错误: INSTALL_FAILED_VERSION_DOWNGRADE
# 解决方案:
adb uninstall com.superhexa.supervision
./gradlew installDebug

# 错误: INSTALL_FAILED_INSUFFICIENT_STORAGE
# 解决方案:
adb shell pm clear com.superhexa.supervision
```

### 2. 调试技巧

#### 启用详细日志
```bash
# 启用调试模式
./run_screenshots.sh --debug

# 查看实时日志
tail -f screenshot_automation/screenshot_automation.log

# 过滤特定日志
grep "ERROR" screenshot_automation/screenshot_automation.log
```

#### 单步调试
```kotlin
// 在关键位置添加调试日志
LogUtils.debug("开始导航到页面: $route")
LogUtils.debug("当前页面状态: ${getCurrentPageState()}")
LogUtils.debug("截图文件路径: ${screenshotFile.absolutePath}")
```

#### 截图验证
```bash
# 手动验证截图
adb shell screencap -p /sdcard/manual_screenshot.png
adb pull /sdcard/manual_screenshot.png
```

### 3. 性能优化

#### 减少执行时间
```yaml
execution:
  page_load_timeout: 3000    # 减少等待时间
  state_change_timeout: 2000
  screenshot_interval: 500
```

#### 内存优化
```kotlin
// 及时释放资源
class ScreenshotController {
    override fun cleanup() {
        bitmapCache.clear()
        screenshotBuffer.clear()
        System.gc()
    }
}
```

### 4. 问题报告

如遇到无法解决的问题，请提供以下信息：

1. **环境信息**
   - Android版本
   - 设备型号
   - 应用版本

2. **错误信息**
   - 完整的错误日志
   - 复现步骤
   - 预期结果vs实际结果

3. **配置信息**
   - 使用的配置文件
   - 执行命令
   - 相关设置

---

通过遵循本指南，您应该能够成功配置和使用昆明项目自动化截图工具。如有任何问题，请参考故障排除部分或联系开发团队。
