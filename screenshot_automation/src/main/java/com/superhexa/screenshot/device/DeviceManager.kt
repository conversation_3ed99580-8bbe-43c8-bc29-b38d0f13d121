package com.superhexa.screenshot.device

import android.content.Context
import android.graphics.Point
import android.os.Build
import android.provider.Settings
import android.view.WindowManager
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.utils.LogUtils
import java.io.IOException

/**
 * 设备管理器 - 负责设备配置和管理
 * 
 * 主要功能：
 * 1. 设备信息获取
 * 2. 屏幕尺寸配置
 * 3. 设备状态管理
 * 4. 系统设置调整
 */
class DeviceManager(
    private val uiDevice: UiDevice,
    private val config: ScreenshotConfig
) {
    
    private val context: Context = InstrumentationRegistry.getInstrumentation().targetContext
    private var originalScreenSize: Point? = null
    private var originalDensity: Float? = null
    private var originalOrientation: Int? = null
    
    init {
        // 保存原始设备配置
        saveOriginalConfiguration()
    }
    
    /**
     * 配置设备参数
     */
    fun configureDevice(deviceConfig: ScreenshotConfig.DeviceConfig): Boolean {
        LogUtils.info("配置设备：${deviceConfig.name} (${deviceConfig.width}x${deviceConfig.height})")
        
        return try {
            // 设置屏幕方向为竖屏
            setScreenOrientation(0)
            
            // 设置屏幕尺寸（如果支持）
            setScreenSize(deviceConfig.width, deviceConfig.height)
            
            // 设置屏幕密度（如果支持）
            setScreenDensity(deviceConfig.density)
            
            // 等待配置生效
            Thread.sleep(2000)
            
            // 验证配置是否生效
            val currentSize = getCurrentScreenSize()
            LogUtils.info("当前屏幕尺寸：${currentSize.x}x${currentSize.y}")
            
            true
        } catch (e: Exception) {
            LogUtils.error("配置设备失败：${deviceConfig.name}", e)
            false
        }
    }
    
    /**
     * 保存原始设备配置
     */
    private fun saveOriginalConfiguration() {
        try {
            originalScreenSize = getCurrentScreenSize()
            originalDensity = getCurrentDensity()
            originalOrientation = getCurrentOrientation()
            
            LogUtils.info("保存原始设备配置：${originalScreenSize?.x}x${originalScreenSize?.y}, 密度：$originalDensity, 方向：$originalOrientation")
        } catch (e: Exception) {
            LogUtils.warning("保存原始设备配置失败", e)
        }
    }
    
    /**
     * 恢复原始设备配置
     */
    fun restoreOriginalConfiguration(): Boolean {
        return try {
            originalScreenSize?.let { size ->
                setScreenSize(size.x, size.y)
            }
            
            originalDensity?.let { density ->
                setScreenDensity(density.toDouble())
            }
            
            originalOrientation?.let { orientation ->
                setScreenOrientation(orientation)
            }
            
            Thread.sleep(2000)
            LogUtils.info("恢复原始设备配置成功")
            true
        } catch (e: Exception) {
            LogUtils.error("恢复原始设备配置失败", e)
            false
        }
    }
    
    /**
     * 设置屏幕尺寸
     */
    private fun setScreenSize(width: Int, height: Int): Boolean {
        return try {
            // 方法1：使用adb命令设置屏幕尺寸
            val result = executeShellCommand("wm size ${width}x${height}")
            if (result.contains("Physical size")) {
                LogUtils.debug("使用wm命令设置屏幕尺寸成功")
                return true
            }
            
            // 方法2：使用系统API（需要系统权限）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                try {
                    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                    // 这里需要系统级权限，通常无法在普通应用中使用
                    LogUtils.debug("系统API设置屏幕尺寸需要系统权限")
                } catch (e: Exception) {
                    LogUtils.debug("系统API设置屏幕尺寸失败", e)
                }
            }
            
            false
        } catch (e: Exception) {
            LogUtils.warning("设置屏幕尺寸失败：${width}x${height}", e)
            false
        }
    }
    
    /**
     * 设置屏幕密度
     */
    private fun setScreenDensity(density: Double): Boolean {
        return try {
            val densityDpi = (density * 160).toInt()
            val result = executeShellCommand("wm density $densityDpi")
            
            if (result.contains("Physical density")) {
                LogUtils.debug("设置屏幕密度成功：$densityDpi")
                true
            } else {
                LogUtils.debug("设置屏幕密度可能失败：$result")
                false
            }
        } catch (e: Exception) {
            LogUtils.warning("设置屏幕密度失败：$density", e)
            false
        }
    }
    
    /**
     * 设置屏幕方向
     */
    private fun setScreenOrientation(orientation: Int): Boolean {
        return try {
            when (orientation) {
                0 -> uiDevice.setOrientationNatural() // 竖屏
                1 -> uiDevice.setOrientationLeft()    // 左横屏
                2 -> uiDevice.setOrientationRight()   // 右横屏
                else -> uiDevice.setOrientationNatural()
            }
            
            // 等待方向切换完成
            Thread.sleep(1000)
            LogUtils.debug("设置屏幕方向成功：$orientation")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置屏幕方向失败：$orientation", e)
            false
        }
    }
    
    /**
     * 获取当前屏幕尺寸
     */
    fun getCurrentScreenSize(): Point {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val size = Point()
        display.getSize(size)
        return size
    }
    
    /**
     * 获取当前屏幕密度
     */
    private fun getCurrentDensity(): Float {
        return context.resources.displayMetrics.density
    }
    
    /**
     * 获取当前屏幕方向
     */
    private fun getCurrentOrientation(): Int {
        return context.resources.configuration.orientation
    }
    
    /**
     * 执行Shell命令
     */
    private fun executeShellCommand(command: String): String {
        return try {
            uiDevice.executeShellCommand(command)
        } catch (e: IOException) {
            LogUtils.warning("执行Shell命令失败：$command", e)
            ""
        }
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): DeviceInfo {
        val size = getCurrentScreenSize()
        val density = getCurrentDensity()
        val orientation = getCurrentOrientation()
        
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            screenWidth = size.x,
            screenHeight = size.y,
            density = density,
            orientation = orientation
        )
    }
    
    /**
     * 检查设备是否支持尺寸调整
     */
    fun isSizeAdjustmentSupported(): Boolean {
        return try {
            val result = executeShellCommand("wm size")
            result.isNotEmpty() && !result.contains("not found")
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查设备是否支持密度调整
     */
    fun isDensityAdjustmentSupported(): Boolean {
        return try {
            val result = executeShellCommand("wm density")
            result.isNotEmpty() && !result.contains("not found")
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 设置系统设置
     */
    fun configureSystemSettings(): Boolean {
        return try {
            // 关闭动画以提高截图稳定性
            disableAnimations()
            
            // 设置屏幕常亮
            keepScreenOn(true)
            
            // 设置最高亮度
            setScreenBrightness(255)
            
            LogUtils.info("系统设置配置完成")
            true
        } catch (e: Exception) {
            LogUtils.error("配置系统设置失败", e)
            false
        }
    }
    
    /**
     * 禁用动画
     */
    private fun disableAnimations(): Boolean {
        return try {
            val commands = listOf(
                "settings put global window_animation_scale 0",
                "settings put global transition_animation_scale 0",
                "settings put global animator_duration_scale 0"
            )
            
            commands.forEach { command ->
                executeShellCommand(command)
            }
            
            LogUtils.debug("禁用动画成功")
            true
        } catch (e: Exception) {
            LogUtils.warning("禁用动画失败", e)
            false
        }
    }
    
    /**
     * 设置屏幕常亮
     */
    private fun keepScreenOn(keepOn: Boolean): Boolean {
        return try {
            val value = if (keepOn) 1 else 0
            executeShellCommand("settings put system screen_off_timeout ${if (keepOn) 2147483647 else 30000}")
            LogUtils.debug("设置屏幕常亮：$keepOn")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置屏幕常亮失败", e)
            false
        }
    }
    
    /**
     * 设置屏幕亮度
     */
    private fun setScreenBrightness(brightness: Int): Boolean {
        return try {
            val clampedBrightness = brightness.coerceIn(0, 255)
            executeShellCommand("settings put system screen_brightness $clampedBrightness")
            LogUtils.debug("设置屏幕亮度：$clampedBrightness")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置屏幕亮度失败", e)
            false
        }
    }
    
    /**
     * 恢复系统设置
     */
    fun restoreSystemSettings(): Boolean {
        return try {
            // 恢复动画
            val commands = listOf(
                "settings put global window_animation_scale 1",
                "settings put global transition_animation_scale 1",
                "settings put global animator_duration_scale 1"
            )
            
            commands.forEach { command ->
                executeShellCommand(command)
            }
            
            // 恢复屏幕超时
            executeShellCommand("settings put system screen_off_timeout 30000")
            
            LogUtils.info("恢复系统设置成功")
            true
        } catch (e: Exception) {
            LogUtils.error("恢复系统设置失败", e)
            false
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            restoreOriginalConfiguration()
            restoreSystemSettings()
            LogUtils.info("设备管理器清理完成")
        } catch (e: Exception) {
            LogUtils.error("设备管理器清理失败", e)
        }
    }
}

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val androidVersion: String,
    val apiLevel: Int,
    val screenWidth: Int,
    val screenHeight: Int,
    val density: Float,
    val orientation: Int
) {
    override fun toString(): String {
        return "$manufacturer $model (Android $androidVersion, API $apiLevel) - ${screenWidth}x${screenHeight} @${density}x"
    }
}
