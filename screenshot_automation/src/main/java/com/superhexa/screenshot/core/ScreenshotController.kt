package com.superhexa.screenshot.core

import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.device.DeviceManager
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.state.StateManager
import com.superhexa.screenshot.utils.FileUtils
import com.superhexa.screenshot.utils.LogUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 截图控制器 - 负责协调整个截图流程
 * 
 * 主要功能：
 * 1. 初始化各个管理器
 * 2. 执行截图流程
 * 3. 管理截图生命周期
 * 4. 处理异常和重试
 */
class ScreenshotController(
    private val context: Context = InstrumentationRegistry.getInstrumentation().targetContext
) {
    
    private val uiDevice: UiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    private val config: ScreenshotConfig = ScreenshotConfig.load()
    
    // 各个管理器
    private val deviceManager = DeviceManager(uiDevice, config)
    private val navigationManager = NavigationManager(context, config)
    private val stateManager = StateManager(context, config)
    
    // 截图统计信息
    private var totalScreenshots = 0
    private var successfulScreenshots = 0
    private var failedScreenshots = 0
    
    /**
     * 开始执行截图流程
     */
    fun startScreenshotProcess(): ScreenshotResult {
        LogUtils.info("开始执行截图流程")
        
        try {
            // 1. 初始化设备和应用
            initializeApp()
            
            // 2. 遍历所有配置的设备尺寸
            config.devices.phone.forEach { deviceConfig ->
                processDeviceScreenshots(deviceConfig)
            }
            
            config.devices.tablet.forEach { deviceConfig ->
                processDeviceScreenshots(deviceConfig)
            }
            
            // 3. 生成截图报告
            val result = generateScreenshotReport()
            
            LogUtils.info("截图流程完成，总计：$totalScreenshots 张，成功：$successfulScreenshots 张，失败：$failedScreenshots 张")
            
            return result
            
        } catch (e: Exception) {
            LogUtils.error("截图流程执行失败", e)
            return ScreenshotResult.failure(e.message ?: "未知错误")
        }
    }
    
    /**
     * 初始化应用
     */
    private fun initializeApp() {
        LogUtils.info("初始化应用")
        
        // 启动应用
        val packageName = config.project.packageName
        val intent = context.packageManager.getLaunchIntentForPackage(packageName)
        intent?.let {
            it.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK)
            context.startActivity(it)
        }
        
        // 等待应用启动
        uiDevice.wait(androidx.test.uiautomator.Until.hasObject(
            androidx.test.uiautomator.By.pkg(packageName)
        ), config.execution.pageLoadTimeout.toLong())
    }
    
    /**
     * 处理特定设备配置的截图
     */
    private fun processDeviceScreenshots(deviceConfig: ScreenshotConfig.DeviceConfig) {
        LogUtils.info("开始处理设备：${deviceConfig.name}")
        
        try {
            // 设置设备尺寸（如果支持）
            deviceManager.configureDevice(deviceConfig)
            
            // 遍历所有模块
            config.modules.forEach { (moduleName, moduleConfig) ->
                if (shouldProcessModule(moduleName)) {
                    processModuleScreenshots(moduleName, moduleConfig, deviceConfig)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("处理设备 ${deviceConfig.name} 时发生错误", e)
        }
    }
    
    /**
     * 处理模块截图
     */
    private fun processModuleScreenshots(
        moduleName: String,
        moduleConfig: ScreenshotConfig.ModuleConfig,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ) {
        LogUtils.info("开始处理模块：${moduleConfig.name}")
        
        moduleConfig.fragments.forEach { fragmentConfig ->
            if (shouldProcessFragment(fragmentConfig.name)) {
                processFragmentScreenshots(moduleName, fragmentConfig, deviceConfig)
            }
        }
    }
    
    /**
     * 处理Fragment截图
     */
    private fun processFragmentScreenshots(
        moduleName: String,
        fragmentConfig: ScreenshotConfig.FragmentConfig,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ) {
        LogUtils.info("开始处理Fragment：${fragmentConfig.name}")
        
        try {
            // 导航到目标Fragment
            val navigated = navigationManager.navigateToFragment(fragmentConfig.route)
            if (!navigated) {
                LogUtils.warning("无法导航到Fragment：${fragmentConfig.name}")
                return
            }
            
            // 等待页面加载
            Thread.sleep(config.execution.pageLoadTimeout.toLong())
            
            // 遍历所有状态
            fragmentConfig.states.forEach { state ->
                if (shouldProcessState(state)) {
                    processStateScreenshot(moduleName, fragmentConfig.name, state, deviceConfig)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("处理Fragment ${fragmentConfig.name} 时发生错误", e)
        }
    }
    
    /**
     * 处理特定状态的截图
     */
    private fun processStateScreenshot(
        moduleName: String,
        fragmentName: String,
        state: String,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ) {
        LogUtils.info("开始截图状态：$state")
        
        var retryCount = 0
        val maxRetries = config.execution.retryCount
        
        while (retryCount <= maxRetries) {
            try {
                // 设置状态
                val stateSet = stateManager.setState(state)
                if (!stateSet) {
                    LogUtils.warning("无法设置状态：$state")
                    break
                }
                
                // 等待状态生效
                Thread.sleep(config.execution.stateChangeTimeout.toLong())
                
                // 执行截图
                val screenshot = takeScreenshot(moduleName, fragmentName, state, deviceConfig)
                if (screenshot != null) {
                    successfulScreenshots++
                    LogUtils.info("截图成功：${screenshot.absolutePath}")
                    break
                } else {
                    throw Exception("截图失败")
                }
                
            } catch (e: Exception) {
                retryCount++
                LogUtils.warning("截图失败，重试 $retryCount/$maxRetries", e)
                
                if (retryCount > maxRetries) {
                    failedScreenshots++
                    LogUtils.error("截图最终失败：$moduleName/$fragmentName/$state")
                }
                
                Thread.sleep(1000) // 重试前等待
            }
        }
        
        totalScreenshots++
        
        // 截图间隔
        Thread.sleep(config.execution.screenshotInterval.toLong())
    }
    
    /**
     * 执行截图
     */
    private fun takeScreenshot(
        moduleName: String,
        fragmentName: String,
        state: String,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ): File? {
        try {
            // 生成文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = config.output.namingPattern
                .replace("{module}", moduleName)
                .replace("{page}", fragmentName)
                .replace("{state}", state)
                .replace("{device}", deviceConfig.name.replace(" ", "_"))
                .replace("{timestamp}", timestamp)
            
            // 创建输出目录
            val outputDir = File(config.output.basePath, "$moduleName/$fragmentName")
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            // 截图文件
            val screenshotFile = File(outputDir, "$fileName.${config.output.format}")
            
            // 执行截图
            val success = uiDevice.takeScreenshot(screenshotFile)
            
            if (success) {
                // 生成元数据文件
                generateMetadata(screenshotFile, moduleName, fragmentName, state, deviceConfig)
                return screenshotFile
            }
            
        } catch (e: Exception) {
            LogUtils.error("截图执行失败", e)
        }
        
        return null
    }
    
    /**
     * 生成截图元数据
     */
    private fun generateMetadata(
        screenshotFile: File,
        moduleName: String,
        fragmentName: String,
        state: String,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ) {
        val metadata = mapOf(
            "screenshot_file" to screenshotFile.name,
            "module" to moduleName,
            "fragment" to fragmentName,
            "state" to state,
            "device" to deviceConfig.name,
            "device_width" to deviceConfig.width,
            "device_height" to deviceConfig.height,
            "device_density" to deviceConfig.density,
            "timestamp" to SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()),
            "file_size" to screenshotFile.length()
        )
        
        val metadataFile = File(screenshotFile.parent, "${screenshotFile.nameWithoutExtension}.json")
        FileUtils.writeJson(metadataFile, metadata)
    }
    
    /**
     * 生成截图报告
     */
    private fun generateScreenshotReport(): ScreenshotResult {
        val report = ScreenshotReport(
            totalScreenshots = totalScreenshots,
            successfulScreenshots = successfulScreenshots,
            failedScreenshots = failedScreenshots,
            successRate = if (totalScreenshots > 0) (successfulScreenshots.toDouble() / totalScreenshots * 100) else 0.0,
            timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        )
        
        // 保存报告
        val reportFile = File(config.output.basePath, "screenshot_report.json")
        FileUtils.writeJson(reportFile, report)
        
        return ScreenshotResult.success(report)
    }
    
    // 过滤方法
    private fun shouldProcessModule(moduleName: String): Boolean {
        return when {
            config.filters.includeModules.isNotEmpty() -> moduleName in config.filters.includeModules
            config.filters.excludeModules.isNotEmpty() -> moduleName !in config.filters.excludeModules
            else -> true
        }
    }
    
    private fun shouldProcessFragment(fragmentName: String): Boolean {
        return fragmentName !in config.filters.skipPages
    }
    
    private fun shouldProcessState(state: String): Boolean {
        return state !in config.filters.skipStates
    }
}

/**
 * 截图结果
 */
sealed class ScreenshotResult {
    data class Success(val report: ScreenshotReport) : ScreenshotResult()
    data class Failure(val error: String) : ScreenshotResult()
    
    companion object {
        fun success(report: ScreenshotReport) = Success(report)
        fun failure(error: String) = Failure(error)
    }
}

/**
 * 截图报告
 */
data class ScreenshotReport(
    val totalScreenshots: Int,
    val successfulScreenshots: Int,
    val failedScreenshots: Int,
    val successRate: Double,
    val timestamp: String
)
