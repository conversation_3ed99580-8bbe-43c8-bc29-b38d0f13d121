package com.superhexa.screenshot.organization

import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.utils.FileUtils
import com.superhexa.screenshot.utils.LogUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 截图组织和命名系统
 * 
 * 主要功能：
 * 1. 智能文件命名
 * 2. 目录结构组织
 * 3. 文件分类管理
 * 4. 批量重命名和整理
 */
class ScreenshotOrganizer(
    private val config: ScreenshotConfig
) {
    
    // 命名模板
    private val namingTemplates = mapOf(
        "default" to "{module}_{fragment}_{state}_{device}_{timestamp}",
        "simple" to "{module}_{fragment}_{timestamp}",
        "detailed" to "{project}_{module}_{fragment}_{state}_{device}_{resolution}_{timestamp}",
        "hierarchical" to "{category}/{module}/{fragment}_{state}_{device}_{timestamp}",
        "localized" to "{module}_{fragment}_{state}_{device}_{locale}_{timestamp}"
    )
    
    // 目录结构模板
    private val directoryTemplates = mapOf(
        "by_module" to "{base_path}/{module}/{fragment}",
        "by_type" to "{base_path}/{type}/{module}",
        "by_date" to "{base_path}/{date}/{module}/{fragment}",
        "by_device" to "{base_path}/{device_type}/{module}/{fragment}",
        "hierarchical" to "{base_path}/{category}/{module}/{fragment}/{type}"
    )
    
    // 文件分类规则
    private val classificationRules = mapOf(
        "ui_states" to listOf("loading", "empty", "error", "success"),
        "user_flows" to listOf("login", "register", "onboarding", "checkout"),
        "responsive" to listOf("phone", "tablet", "landscape", "portrait"),
        "permissions" to listOf("granted", "denied", "request", "degraded"),
        "localization" to listOf("zh", "en", "ja", "ko", "es", "fr")
    )
    
    /**
     * 组织截图文件
     */
    fun organizeScreenshots(
        screenshotFiles: List<File>,
        organizationStrategy: OrganizationStrategy = OrganizationStrategy.BY_MODULE
    ): OrganizationResult {
        
        LogUtils.info("开始组织截图文件，共 ${screenshotFiles.size} 个文件")
        
        val result = OrganizationResult()
        
        try {
            // 1. 分析现有文件
            val fileAnalysis = analyzeScreenshotFiles(screenshotFiles)
            
            // 2. 生成新的组织结构
            val newStructure = generateOrganizationStructure(fileAnalysis, organizationStrategy)
            
            // 3. 执行文件移动和重命名
            val moveResult = executeFileOrganization(fileAnalysis, newStructure)
            
            // 4. 生成索引文件
            generateIndexFiles(newStructure)
            
            // 5. 创建快捷访问链接
            createQuickAccessLinks(newStructure)
            
            result.success = true
            result.processedFiles = moveResult.processedFiles
            result.newStructure = newStructure
            result.summary = generateOrganizationSummary(moveResult)
            
            LogUtils.info("截图文件组织完成，处理 ${result.processedFiles} 个文件")
            
        } catch (e: Exception) {
            LogUtils.error("截图文件组织失败", e)
            result.success = false
            result.error = e.message
        }
        
        return result
    }
    
    /**
     * 分析截图文件
     */
    private fun analyzeScreenshotFiles(files: List<File>): List<ScreenshotFileInfo> {
        LogUtils.info("分析截图文件")
        
        return files.mapNotNull { file ->
            try {
                if (file.exists() && file.extension.lowercase() in listOf("png", "jpg", "jpeg")) {
                    parseScreenshotFileName(file)
                } else {
                    null
                }
            } catch (e: Exception) {
                LogUtils.warning("分析文件失败：${file.name}", e)
                null
            }
        }
    }
    
    /**
     * 解析截图文件名
     */
    private fun parseScreenshotFileName(file: File): ScreenshotFileInfo {
        val fileName = file.nameWithoutExtension
        val parts = fileName.split("_")
        
        // 尝试从文件名中提取信息
        val info = ScreenshotFileInfo(
            originalFile = file,
            fileName = fileName,
            extension = file.extension
        )
        
        // 解析文件名组件
        when {
            parts.size >= 5 -> {
                // 标准格式：module_fragment_state_device_timestamp
                info.module = parts[0]
                info.fragment = parts[1]
                info.state = parts[2]
                info.device = parts[3]
                info.timestamp = parts.getOrNull(4) ?: ""
            }
            parts.size >= 3 -> {
                // 简化格式：module_fragment_timestamp
                info.module = parts[0]
                info.fragment = parts[1]
                info.timestamp = parts.getOrNull(2) ?: ""
            }
            else -> {
                // 未知格式，使用默认值
                info.module = "unknown"
                info.fragment = "unknown"
            }
        }
        
        // 分析文件特征
        info.category = classifyScreenshot(info)
        info.deviceType = determineDeviceType(info.device)
        info.screenshotType = determineScreenshotType(info)
        info.fileSize = file.length()
        info.lastModified = file.lastModified()
        
        return info
    }
    
    /**
     * 分类截图
     */
    private fun classifyScreenshot(info: ScreenshotFileInfo): String {
        return when {
            info.state in classificationRules["ui_states"]!! -> "ui_states"
            info.fragment in classificationRules["user_flows"]!! -> "user_flows"
            info.device.contains("tablet", ignoreCase = true) -> "responsive"
            info.fileName.contains("permission", ignoreCase = true) -> "permissions"
            info.fileName.contains("locale", ignoreCase = true) -> "localization"
            else -> "general"
        }
    }
    
    /**
     * 确定设备类型
     */
    private fun determineDeviceType(device: String): String {
        return when {
            device.contains("tablet", ignoreCase = true) -> "tablet"
            device.contains("fold", ignoreCase = true) -> "foldable"
            device.contains("phone", ignoreCase = true) -> "phone"
            else -> "unknown"
        }
    }
    
    /**
     * 确定截图类型
     */
    private fun determineScreenshotType(info: ScreenshotFileInfo): String {
        return when {
            info.fileName.contains("responsive", ignoreCase = true) -> "responsive"
            info.fileName.contains("permission", ignoreCase = true) -> "permission"
            info.fileName.contains("state", ignoreCase = true) -> "state"
            info.fileName.contains("flow", ignoreCase = true) -> "flow"
            else -> "standard"
        }
    }
    
    /**
     * 生成组织结构
     */
    private fun generateOrganizationStructure(
        fileAnalysis: List<ScreenshotFileInfo>,
        strategy: OrganizationStrategy
    ): OrganizationStructure {
        
        LogUtils.info("生成组织结构：${strategy.name}")
        
        val structure = OrganizationStructure()
        
        fileAnalysis.forEach { fileInfo ->
            val targetPath = generateTargetPath(fileInfo, strategy)
            val newFileName = generateOptimizedFileName(fileInfo)
            
            structure.fileMapping[fileInfo.originalFile] = FileMapping(
                originalFile = fileInfo.originalFile,
                targetDirectory = File(targetPath),
                newFileName = newFileName,
                fileInfo = fileInfo
            )
        }
        
        return structure
    }
    
    /**
     * 生成目标路径
     */
    private fun generateTargetPath(fileInfo: ScreenshotFileInfo, strategy: OrganizationStrategy): String {
        val template = when (strategy) {
            OrganizationStrategy.BY_MODULE -> directoryTemplates["by_module"]!!
            OrganizationStrategy.BY_TYPE -> directoryTemplates["by_type"]!!
            OrganizationStrategy.BY_DATE -> directoryTemplates["by_date"]!!
            OrganizationStrategy.BY_DEVICE -> directoryTemplates["by_device"]!!
            OrganizationStrategy.HIERARCHICAL -> directoryTemplates["hierarchical"]!!
        }
        
        return template
            .replace("{base_path}", config.output.basePath)
            .replace("{module}", fileInfo.module)
            .replace("{fragment}", fileInfo.fragment)
            .replace("{type}", fileInfo.screenshotType)
            .replace("{device_type}", fileInfo.deviceType)
            .replace("{category}", fileInfo.category)
            .replace("{date}", SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(fileInfo.lastModified)))
    }
    
    /**
     * 生成优化的文件名
     */
    private fun generateOptimizedFileName(fileInfo: ScreenshotFileInfo): String {
        val template = config.output.namingPattern ?: namingTemplates["default"]!!
        
        val timestamp = if (fileInfo.timestamp.isNotEmpty()) {
            fileInfo.timestamp
        } else {
            SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date(fileInfo.lastModified))
        }
        
        return template
            .replace("{project}", config.project.name.replace(" ", "_"))
            .replace("{module}", fileInfo.module)
            .replace("{fragment}", fileInfo.fragment)
            .replace("{state}", fileInfo.state)
            .replace("{device}", fileInfo.device)
            .replace("{timestamp}", timestamp)
            .replace("{resolution}", "${fileInfo.width}x${fileInfo.height}")
            .replace("{category}", fileInfo.category)
            .replace("{locale}", "zh_CN") // 默认中文
            + ".${fileInfo.extension}"
    }
    
    /**
     * 执行文件组织
     */
    private fun executeFileOrganization(
        fileAnalysis: List<ScreenshotFileInfo>,
        structure: OrganizationStructure
    ): MoveResult {
        
        LogUtils.info("执行文件组织")
        
        val result = MoveResult()
        
        structure.fileMapping.values.forEach { mapping ->
            try {
                // 创建目标目录
                if (!mapping.targetDirectory.exists()) {
                    mapping.targetDirectory.mkdirs()
                }
                
                // 生成目标文件
                val targetFile = File(mapping.targetDirectory, mapping.newFileName)
                
                // 避免文件名冲突
                val finalTargetFile = resolveFileNameConflict(targetFile)
                
                // 移动文件
                if (mapping.originalFile.renameTo(finalTargetFile)) {
                    result.processedFiles++
                    result.successfulMoves.add(mapping.originalFile.name to finalTargetFile.absolutePath)
                    LogUtils.debug("文件移动成功：${mapping.originalFile.name} -> ${finalTargetFile.name}")
                } else {
                    // 如果重命名失败，尝试复制
                    if (FileUtils.copyFile(mapping.originalFile, finalTargetFile)) {
                        mapping.originalFile.delete()
                        result.processedFiles++
                        result.successfulMoves.add(mapping.originalFile.name to finalTargetFile.absolutePath)
                        LogUtils.debug("文件复制成功：${mapping.originalFile.name} -> ${finalTargetFile.name}")
                    } else {
                        result.failedMoves.add(mapping.originalFile.name to "移动失败")
                        LogUtils.warning("文件移动失败：${mapping.originalFile.name}")
                    }
                }
                
            } catch (e: Exception) {
                result.failedMoves.add(mapping.originalFile.name to e.message ?: "未知错误")
                LogUtils.error("文件组织失败：${mapping.originalFile.name}", e)
            }
        }
        
        return result
    }
    
    /**
     * 解决文件名冲突
     */
    private fun resolveFileNameConflict(targetFile: File): File {
        if (!targetFile.exists()) {
            return targetFile
        }
        
        val nameWithoutExt = targetFile.nameWithoutExtension
        val extension = targetFile.extension
        val parentDir = targetFile.parentFile
        
        var counter = 1
        var newFile: File
        
        do {
            val newName = "${nameWithoutExt}_$counter.$extension"
            newFile = File(parentDir, newName)
            counter++
        } while (newFile.exists())
        
        return newFile
    }
    
    /**
     * 生成索引文件
     */
    private fun generateIndexFiles(structure: OrganizationStructure) {
        LogUtils.info("生成索引文件")
        
        try {
            // 按模块生成索引
            val moduleGroups = structure.fileMapping.values.groupBy { it.fileInfo.module }
            
            moduleGroups.forEach { (module, mappings) ->
                val indexFile = File(config.output.basePath, "$module/index.md")
                val indexContent = generateModuleIndex(module, mappings)
                FileUtils.writeTextFile(indexFile, indexContent)
            }
            
            // 生成总索引
            val masterIndexFile = File(config.output.basePath, "README.md")
            val masterIndexContent = generateMasterIndex(structure)
            FileUtils.writeTextFile(masterIndexFile, masterIndexContent)
            
        } catch (e: Exception) {
            LogUtils.error("生成索引文件失败", e)
        }
    }
    
    /**
     * 生成模块索引
     */
    private fun generateModuleIndex(module: String, mappings: List<FileMapping>): String {
        return buildString {
            appendLine("# $module 模块截图索引")
            appendLine()
            appendLine("## 统计信息")
            appendLine("- 总截图数：${mappings.size}")
            appendLine("- 最后更新：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
            appendLine()
            
            // 按Fragment分组
            val fragmentGroups = mappings.groupBy { it.fileInfo.fragment }
            
            fragmentGroups.forEach { (fragment, fragmentMappings) ->
                appendLine("## $fragment")
                appendLine()
                
                fragmentMappings.forEach { mapping ->
                    val relativePath = File(config.output.basePath).toURI().relativize(
                        File(mapping.targetDirectory, mapping.newFileName).toURI()
                    ).path
                    
                    appendLine("- [${mapping.newFileName}]($relativePath)")
                    appendLine("  - 状态：${mapping.fileInfo.state}")
                    appendLine("  - 设备：${mapping.fileInfo.device}")
                    appendLine("  - 类型：${mapping.fileInfo.screenshotType}")
                    appendLine()
                }
            }
        }
    }
    
    /**
     * 生成主索引
     */
    private fun generateMasterIndex(structure: OrganizationStructure): String {
        return buildString {
            appendLine("# 昆明项目截图库")
            appendLine()
            appendLine("## 概览")
            appendLine("- 总截图数：${structure.fileMapping.size}")
            appendLine("- 生成时间：${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
            appendLine()
            
            // 模块统计
            val moduleStats = structure.fileMapping.values.groupBy { it.fileInfo.module }
            appendLine("## 模块统计")
            moduleStats.forEach { (module, mappings) ->
                appendLine("- [$module]($module/index.md)：${mappings.size} 张截图")
            }
            appendLine()
            
            // 类型统计
            val typeStats = structure.fileMapping.values.groupBy { it.fileInfo.screenshotType }
            appendLine("## 类型统计")
            typeStats.forEach { (type, mappings) ->
                appendLine("- $type：${mappings.size} 张")
            }
            appendLine()
            
            appendLine("## 使用说明")
            appendLine("1. 点击模块名称查看该模块的详细截图索引")
            appendLine("2. 截图按功能模块和页面组织")
            appendLine("3. 每张截图都包含详细的元数据信息")
            appendLine("4. 支持按设备类型、状态、时间等维度筛选")
        }
    }
    
    /**
     * 创建快捷访问链接
     */
    private fun createQuickAccessLinks(structure: OrganizationStructure) {
        LogUtils.info("创建快捷访问链接")
        
        try {
            val quickAccessDir = File(config.output.basePath, "quick_access")
            if (!quickAccessDir.exists()) {
                quickAccessDir.mkdirs()
            }
            
            // 按类型创建链接
            val typeGroups = structure.fileMapping.values.groupBy { it.fileInfo.screenshotType }
            
            typeGroups.forEach { (type, mappings) ->
                val typeDir = File(quickAccessDir, type)
                if (!typeDir.exists()) {
                    typeDir.mkdirs()
                }
                
                mappings.forEach { mapping ->
                    val targetFile = File(mapping.targetDirectory, mapping.newFileName)
                    val linkFile = File(typeDir, mapping.newFileName)
                    
                    // 创建符号链接（在支持的系统上）
                    try {
                        if (!linkFile.exists()) {
                            java.nio.file.Files.createSymbolicLink(
                                linkFile.toPath(),
                                targetFile.toPath()
                            )
                        }
                    } catch (e: Exception) {
                        // 如果符号链接失败，创建硬链接或复制文件
                        FileUtils.copyFile(targetFile, linkFile)
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("创建快捷访问链接失败", e)
        }
    }
    
    /**
     * 生成组织摘要
     */
    private fun generateOrganizationSummary(moveResult: MoveResult): String {
        return buildString {
            appendLine("截图组织完成摘要：")
            appendLine("- 处理文件数：${moveResult.processedFiles}")
            appendLine("- 成功移动：${moveResult.successfulMoves.size}")
            appendLine("- 失败移动：${moveResult.failedMoves.size}")
            
            if (moveResult.failedMoves.isNotEmpty()) {
                appendLine("\n失败详情：")
                moveResult.failedMoves.forEach { (file, reason) ->
                    appendLine("- $file: $reason")
                }
            }
        }
    }
    
    /**
     * 批量重命名文件
     */
    fun batchRename(
        files: List<File>,
        namingTemplate: String = namingTemplates["default"]!!
    ): RenameResult {
        
        LogUtils.info("批量重命名文件，共 ${files.size} 个")
        
        val result = RenameResult()
        
        files.forEach { file ->
            try {
                val fileInfo = parseScreenshotFileName(file)
                val newName = generateOptimizedFileName(fileInfo)
                val newFile = File(file.parent, newName)
                
                if (file.renameTo(newFile)) {
                    result.successfulRenames.add(file.name to newName)
                    LogUtils.debug("重命名成功：${file.name} -> $newName")
                } else {
                    result.failedRenames.add(file.name to "重命名失败")
                    LogUtils.warning("重命名失败：${file.name}")
                }
                
            } catch (e: Exception) {
                result.failedRenames.add(file.name to (e.message ?: "未知错误"))
                LogUtils.error("重命名异常：${file.name}", e)
            }
        }
        
        return result
    }
    
    /**
     * 清理重复文件
     */
    fun cleanupDuplicates(directory: File): CleanupResult {
        LogUtils.info("清理重复文件：${directory.absolutePath}")
        
        val result = CleanupResult()
        
        try {
            val files = FileUtils.listFiles(directory, "png", true) + 
                       FileUtils.listFiles(directory, "jpg", true)
            
            val fileGroups = files.groupBy { it.length() } // 按文件大小分组
            
            fileGroups.values.forEach { sameSize ->
                if (sameSize.size > 1) {
                    // 进一步比较文件内容
                    val duplicates = findDuplicatesByContent(sameSize)
                    
                    duplicates.forEach { duplicateGroup ->
                        // 保留第一个文件，删除其他重复文件
                        duplicateGroup.drop(1).forEach { duplicate ->
                            if (duplicate.delete()) {
                                result.deletedFiles.add(duplicate.name)
                                result.freedSpace += duplicate.length()
                            }
                        }
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("清理重复文件失败", e)
            result.error = e.message
        }
        
        return result
    }
    
    /**
     * 按内容查找重复文件
     */
    private fun findDuplicatesByContent(files: List<File>): List<List<File>> {
        val duplicateGroups = mutableListOf<List<File>>()
        val processed = mutableSetOf<File>()
        
        files.forEach { file1 ->
            if (file1 in processed) return@forEach
            
            val duplicates = mutableListOf(file1)
            
            files.forEach { file2 ->
                if (file2 != file1 && file2 !in processed && filesAreIdentical(file1, file2)) {
                    duplicates.add(file2)
                }
            }
            
            if (duplicates.size > 1) {
                duplicateGroups.add(duplicates)
                processed.addAll(duplicates)
            }
        }
        
        return duplicateGroups
    }
    
    /**
     * 检查两个文件是否相同
     */
    private fun filesAreIdentical(file1: File, file2: File): Boolean {
        return try {
            if (file1.length() != file2.length()) {
                false
            } else {
                // 简单的字节比较（对于大文件可能需要优化）
                file1.readBytes().contentEquals(file2.readBytes())
            }
        } catch (e: Exception) {
            false
        }
    }
}

// 数据类定义

data class ScreenshotFileInfo(
    val originalFile: File,
    val fileName: String,
    val extension: String,
    var module: String = "",
    var fragment: String = "",
    var state: String = "",
    var device: String = "",
    var timestamp: String = "",
    var category: String = "",
    var deviceType: String = "",
    var screenshotType: String = "",
    var width: Int = 0,
    var height: Int = 0,
    var fileSize: Long = 0,
    var lastModified: Long = 0
)

data class FileMapping(
    val originalFile: File,
    val targetDirectory: File,
    val newFileName: String,
    val fileInfo: ScreenshotFileInfo
)

data class OrganizationStructure(
    val fileMapping: MutableMap<File, FileMapping> = mutableMapOf()
)

data class OrganizationResult(
    var success: Boolean = false,
    var processedFiles: Int = 0,
    var newStructure: OrganizationStructure? = null,
    var summary: String = "",
    var error: String? = null
)

data class MoveResult(
    var processedFiles: Int = 0,
    val successfulMoves: MutableList<Pair<String, String>> = mutableListOf(),
    val failedMoves: MutableList<Pair<String, String>> = mutableListOf()
)

data class RenameResult(
    val successfulRenames: MutableList<Pair<String, String>> = mutableListOf(),
    val failedRenames: MutableList<Pair<String, String>> = mutableListOf()
)

data class CleanupResult(
    val deletedFiles: MutableList<String> = mutableListOf(),
    var freedSpace: Long = 0,
    var error: String? = null
)

enum class OrganizationStrategy {
    BY_MODULE,
    BY_TYPE,
    BY_DATE,
    BY_DEVICE,
    HIERARCHICAL
}
