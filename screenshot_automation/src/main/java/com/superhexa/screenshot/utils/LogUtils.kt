package com.superhexa.screenshot.utils

import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志工具类 - 提供统一的日志记录功能
 * 
 * 主要功能：
 * 1. 控制台日志输出
 * 2. 文件日志记录
 * 3. 日志级别控制
 * 4. 日志格式化
 */
object LogUtils {
    
    private const val TAG = "ScreenshotAutomation"
    private const val LOG_FILE_NAME = "screenshot_automation.log"
    
    // 日志级别
    enum class LogLevel(val value: Int) {
        VERBOSE(0),
        DEBUG(1),
        INFO(2),
        WARNING(3),
        ERROR(4)
    }
    
    // 当前日志级别
    private var currentLogLevel = LogLevel.DEBUG
    
    // 是否启用文件日志
    private var fileLoggingEnabled = true
    
    // 日志文件路径
    private var logFilePath: String? = null
    
    // 日志格式化器
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    /**
     * 初始化日志工具
     */
    fun init(logLevel: LogLevel = LogLevel.DEBUG, enableFileLogging: Boolean = true, logDir: String? = null) {
        currentLogLevel = logLevel
        fileLoggingEnabled = enableFileLogging
        
        if (enableFileLogging && logDir != null) {
            val logFile = File(logDir, LOG_FILE_NAME)
            logFilePath = logFile.absolutePath
            
            // 创建日志目录
            logFile.parentFile?.mkdirs()
            
            // 清空旧日志文件（可选）
            if (logFile.exists() && logFile.length() > 10 * 1024 * 1024) { // 10MB
                logFile.delete()
            }
            
            info("日志系统初始化完成，日志文件：${logFile.absolutePath}")
        }
    }
    
    /**
     * 设置日志级别
     */
    fun setLogLevel(level: LogLevel) {
        currentLogLevel = level
        info("日志级别设置为：${level.name}")
    }
    
    /**
     * 启用或禁用文件日志
     */
    fun setFileLoggingEnabled(enabled: Boolean) {
        fileLoggingEnabled = enabled
        info("文件日志${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * Verbose级别日志
     */
    fun verbose(message: String, throwable: Throwable? = null) {
        log(LogLevel.VERBOSE, message, throwable)
    }
    
    /**
     * Debug级别日志
     */
    fun debug(message: String, throwable: Throwable? = null) {
        log(LogLevel.DEBUG, message, throwable)
    }
    
    /**
     * Info级别日志
     */
    fun info(message: String, throwable: Throwable? = null) {
        log(LogLevel.INFO, message, throwable)
    }
    
    /**
     * Warning级别日志
     */
    fun warning(message: String, throwable: Throwable? = null) {
        log(LogLevel.WARNING, message, throwable)
    }
    
    /**
     * Error级别日志
     */
    fun error(message: String, throwable: Throwable? = null) {
        log(LogLevel.ERROR, message, throwable)
    }
    
    /**
     * 记录日志
     */
    private fun log(level: LogLevel, message: String, throwable: Throwable? = null) {
        // 检查日志级别
        if (level.value < currentLogLevel.value) {
            return
        }
        
        val timestamp = dateFormat.format(Date())
        val threadName = Thread.currentThread().name
        val stackTrace = Thread.currentThread().stackTrace
        
        // 获取调用者信息
        val callerInfo = getCallerInfo(stackTrace)
        
        // 格式化日志消息
        val formattedMessage = formatLogMessage(timestamp, level, threadName, callerInfo, message)
        
        // 输出到控制台
        outputToConsole(level, formattedMessage, throwable)
        
        // 输出到文件
        if (fileLoggingEnabled) {
            outputToFile(formattedMessage, throwable)
        }
    }
    
    /**
     * 获取调用者信息
     */
    private fun getCallerInfo(stackTrace: Array<StackTraceElement>): String {
        // 跳过LogUtils类的方法调用
        for (i in stackTrace.indices) {
            val element = stackTrace[i]
            if (!element.className.contains("LogUtils")) {
                val className = element.className.substringAfterLast('.')
                val methodName = element.methodName
                val lineNumber = element.lineNumber
                return "$className.$methodName():$lineNumber"
            }
        }
        return "Unknown"
    }
    
    /**
     * 格式化日志消息
     */
    private fun formatLogMessage(
        timestamp: String,
        level: LogLevel,
        threadName: String,
        callerInfo: String,
        message: String
    ): String {
        return "$timestamp [${level.name}] [$threadName] [$callerInfo] $message"
    }
    
    /**
     * 输出到控制台
     */
    private fun outputToConsole(level: LogLevel, message: String, throwable: Throwable?) {
        when (level) {
            LogLevel.VERBOSE -> {
                Log.v(TAG, message, throwable)
            }
            LogLevel.DEBUG -> {
                Log.d(TAG, message, throwable)
            }
            LogLevel.INFO -> {
                Log.i(TAG, message, throwable)
            }
            LogLevel.WARNING -> {
                Log.w(TAG, message, throwable)
            }
            LogLevel.ERROR -> {
                Log.e(TAG, message, throwable)
            }
        }
    }
    
    /**
     * 输出到文件
     */
    private fun outputToFile(message: String, throwable: Throwable?) {
        val filePath = logFilePath ?: return
        
        try {
            FileWriter(filePath, true).use { writer ->
                writer.append(message)
                writer.append("\n")
                
                // 如果有异常，记录异常堆栈
                throwable?.let { t ->
                    writer.append("Exception: ${t.javaClass.simpleName}: ${t.message}\n")
                    t.stackTrace.forEach { element ->
                        writer.append("    at $element\n")
                    }
                    
                    // 记录原因异常
                    var cause = t.cause
                    while (cause != null) {
                        writer.append("Caused by: ${cause.javaClass.simpleName}: ${cause.message}\n")
                        cause.stackTrace.forEach { element ->
                            writer.append("    at $element\n")
                        }
                        cause = cause.cause
                    }
                }
                
                writer.flush()
            }
        } catch (e: IOException) {
            Log.e(TAG, "写入日志文件失败：$filePath", e)
        }
    }
    
    /**
     * 记录方法进入
     */
    fun enterMethod(methodName: String, vararg params: Any?) {
        if (currentLogLevel.value <= LogLevel.DEBUG.value) {
            val paramStr = params.joinToString(", ") { it?.toString() ?: "null" }
            debug(">>> 进入方法：$methodName($paramStr)")
        }
    }
    
    /**
     * 记录方法退出
     */
    fun exitMethod(methodName: String, result: Any? = null) {
        if (currentLogLevel.value <= LogLevel.DEBUG.value) {
            val resultStr = result?.toString() ?: "void"
            debug("<<< 退出方法：$methodName，返回：$resultStr")
        }
    }
    
    /**
     * 记录性能信息
     */
    fun performance(operation: String, duration: Long) {
        info("性能统计：$operation 耗时 ${duration}ms")
    }
    
    /**
     * 记录截图信息
     */
    fun screenshot(fileName: String, success: Boolean, fileSize: Long = 0) {
        if (success) {
            info("截图成功：$fileName，文件大小：${formatFileSize(fileSize)}")
        } else {
            error("截图失败：$fileName")
        }
    }
    
    /**
     * 记录导航信息
     */
    fun navigation(from: String, to: String, success: Boolean) {
        if (success) {
            info("导航成功：$from -> $to")
        } else {
            warning("导航失败：$from -> $to")
        }
    }
    
    /**
     * 记录状态变更
     */
    fun stateChange(from: String, to: String, success: Boolean) {
        if (success) {
            info("状态变更成功：$from -> $to")
        } else {
            warning("状态变更失败：$from -> $to")
        }
    }
    
    /**
     * 记录设备信息
     */
    fun deviceInfo(info: String) {
        info("设备信息：$info")
    }
    
    /**
     * 记录配置信息
     */
    fun config(key: String, value: Any?) {
        debug("配置：$key = $value")
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取日志文件内容
     */
    fun getLogFileContent(): String? {
        val filePath = logFilePath ?: return null
        
        return try {
            File(filePath).readText()
        } catch (e: IOException) {
            error("读取日志文件失败：$filePath", e)
            null
        }
    }
    
    /**
     * 清空日志文件
     */
    fun clearLogFile(): Boolean {
        val filePath = logFilePath ?: return false
        
        return try {
            File(filePath).writeText("")
            info("日志文件已清空")
            true
        } catch (e: IOException) {
            error("清空日志文件失败：$filePath", e)
            false
        }
    }
    
    /**
     * 获取日志文件大小
     */
    fun getLogFileSize(): Long {
        val filePath = logFilePath ?: return 0
        
        return try {
            File(filePath).length()
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 压缩日志文件
     */
    fun compressLogFile(): Boolean {
        val filePath = logFilePath ?: return false
        
        return try {
            val logFile = File(filePath)
            if (!logFile.exists()) return false
            
            val compressedFile = File("${filePath}.gz")
            // 这里可以实现日志文件压缩逻辑
            
            info("日志文件压缩完成：${compressedFile.absolutePath}")
            true
        } catch (e: Exception) {
            error("压缩日志文件失败", e)
            false
        }
    }
}
