package com.superhexa.screenshot.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 文件工具类 - 提供文件操作相关功能
 * 
 * 主要功能：
 * 1. JSON文件读写
 * 2. 文件管理
 * 3. 目录操作
 * 4. 文件格式化
 */
object FileUtils {
    
    private val objectMapper = ObjectMapper().apply {
        registerModule(KotlinModule.Builder().build())
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    /**
     * 写入JSON文件
     */
    fun writeJson(file: File, data: Any): Boolean {
        return try {
            // 确保父目录存在
            file.parentFile?.mkdirs()
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, data)
            LogUtils.debug("写入JSON文件成功：${file.absolutePath}")
            true
        } catch (e: IOException) {
            LogUtils.error("写入JSON文件失败：${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * 读取JSON文件
     */
    inline fun <reified T> readJson(file: File): T? {
        return try {
            if (!file.exists()) {
                LogUtils.warning("JSON文件不存在：${file.absolutePath}")
                return null
            }
            
            objectMapper.readValue(file, T::class.java)
        } catch (e: IOException) {
            LogUtils.error("读取JSON文件失败：${file.absolutePath}", e)
            null
        }
    }
    
    /**
     * 创建目录
     */
    fun createDirectory(path: String): Boolean {
        return try {
            val dir = File(path)
            if (!dir.exists()) {
                val created = dir.mkdirs()
                if (created) {
                    LogUtils.debug("创建目录成功：$path")
                } else {
                    LogUtils.warning("创建目录失败：$path")
                }
                created
            } else {
                LogUtils.debug("目录已存在：$path")
                true
            }
        } catch (e: Exception) {
            LogUtils.error("创建目录异常：$path", e)
            false
        }
    }
    
    /**
     * 删除文件或目录
     */
    fun delete(file: File): Boolean {
        return try {
            if (!file.exists()) {
                LogUtils.debug("文件不存在，无需删除：${file.absolutePath}")
                return true
            }
            
            if (file.isDirectory) {
                // 递归删除目录内容
                file.listFiles()?.forEach { child ->
                    delete(child)
                }
            }
            
            val deleted = file.delete()
            if (deleted) {
                LogUtils.debug("删除成功：${file.absolutePath}")
            } else {
                LogUtils.warning("删除失败：${file.absolutePath}")
            }
            deleted
        } catch (e: Exception) {
            LogUtils.error("删除异常：${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * 复制文件
     */
    fun copyFile(source: File, target: File): Boolean {
        return try {
            if (!source.exists()) {
                LogUtils.warning("源文件不存在：${source.absolutePath}")
                return false
            }
            
            // 确保目标目录存在
            target.parentFile?.mkdirs()
            
            source.copyTo(target, overwrite = true)
            LogUtils.debug("复制文件成功：${source.absolutePath} -> ${target.absolutePath}")
            true
        } catch (e: Exception) {
            LogUtils.error("复制文件失败：${source.absolutePath} -> ${target.absolutePath}", e)
            false
        }
    }
    
    /**
     * 移动文件
     */
    fun moveFile(source: File, target: File): Boolean {
        return try {
            if (copyFile(source, target)) {
                delete(source)
            } else {
                false
            }
        } catch (e: Exception) {
            LogUtils.error("移动文件失败：${source.absolutePath} -> ${target.absolutePath}", e)
            false
        }
    }
    
    /**
     * 获取文件大小
     */
    fun getFileSize(file: File): Long {
        return try {
            if (file.exists()) {
                if (file.isDirectory) {
                    // 计算目录大小
                    file.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
                } else {
                    file.length()
                }
            } else {
                0L
            }
        } catch (e: Exception) {
            LogUtils.warning("获取文件大小失败：${file.absolutePath}", e)
            0L
        }
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> String.format("%.1fKB", bytes / 1024.0)
            bytes < 1024 * 1024 * 1024 -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
            else -> String.format("%.1fGB", bytes / (1024.0 * 1024.0 * 1024.0))
        }
    }
    
    /**
     * 获取文件扩展名
     */
    fun getFileExtension(file: File): String {
        val name = file.name
        val lastDotIndex = name.lastIndexOf('.')
        return if (lastDotIndex > 0 && lastDotIndex < name.length - 1) {
            name.substring(lastDotIndex + 1).lowercase()
        } else {
            ""
        }
    }
    
    /**
     * 获取不带扩展名的文件名
     */
    fun getFileNameWithoutExtension(file: File): String {
        val name = file.name
        val lastDotIndex = name.lastIndexOf('.')
        return if (lastDotIndex > 0) {
            name.substring(0, lastDotIndex)
        } else {
            name
        }
    }
    
    /**
     * 检查文件是否为图片
     */
    fun isImageFile(file: File): Boolean {
        val extension = getFileExtension(file)
        return extension in listOf("png", "jpg", "jpeg", "gif", "bmp", "webp")
    }
    
    /**
     * 生成唯一文件名
     */
    fun generateUniqueFileName(directory: File, baseName: String, extension: String): String {
        var counter = 0
        var fileName = "$baseName.$extension"
        
        while (File(directory, fileName).exists()) {
            counter++
            fileName = "${baseName}_$counter.$extension"
        }
        
        return fileName
    }
    
    /**
     * 清理目录（删除旧文件）
     */
    fun cleanDirectory(directory: File, maxAge: Long = 7 * 24 * 60 * 60 * 1000L): Int {
        var deletedCount = 0
        
        try {
            if (!directory.exists() || !directory.isDirectory) {
                return 0
            }
            
            val currentTime = System.currentTimeMillis()
            
            directory.listFiles()?.forEach { file ->
                if (file.isFile && (currentTime - file.lastModified()) > maxAge) {
                    if (delete(file)) {
                        deletedCount++
                    }
                }
            }
            
            LogUtils.info("清理目录完成：${directory.absolutePath}，删除 $deletedCount 个文件")
        } catch (e: Exception) {
            LogUtils.error("清理目录失败：${directory.absolutePath}", e)
        }
        
        return deletedCount
    }
    
    /**
     * 获取目录下的文件列表
     */
    fun listFiles(directory: File, extension: String? = null, recursive: Boolean = false): List<File> {
        val files = mutableListOf<File>()
        
        try {
            if (!directory.exists() || !directory.isDirectory) {
                return files
            }
            
            val fileSequence = if (recursive) {
                directory.walkTopDown().filter { it.isFile }
            } else {
                directory.listFiles()?.asSequence()?.filter { it.isFile } ?: emptySequence()
            }
            
            fileSequence.forEach { file ->
                if (extension == null || getFileExtension(file) == extension.lowercase()) {
                    files.add(file)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("获取文件列表失败：${directory.absolutePath}", e)
        }
        
        return files.sortedBy { it.name }
    }
    
    /**
     * 创建截图报告HTML文件
     */
    fun createScreenshotReport(
        outputDir: File,
        screenshots: List<ScreenshotInfo>,
        reportTitle: String = "截图报告"
    ): File? {
        return try {
            val reportFile = File(outputDir, "screenshot_report.html")
            val htmlContent = generateReportHtml(screenshots, reportTitle)
            
            reportFile.writeText(htmlContent, Charsets.UTF_8)
            LogUtils.info("生成截图报告：${reportFile.absolutePath}")
            
            reportFile
        } catch (e: Exception) {
            LogUtils.error("生成截图报告失败", e)
            null
        }
    }
    
    /**
     * 生成报告HTML内容
     */
    private fun generateReportHtml(screenshots: List<ScreenshotInfo>, title: String): String {
        val timestamp = dateFormat.format(Date())
        
        return """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>$title</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                    .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .stats { display: flex; gap: 20px; margin: 20px 0; }
                    .stat-card { background: white; padding: 15px; border-radius: 8px; flex: 1; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .stat-number { font-size: 24px; font-weight: bold; color: #2196F3; }
                    .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
                    .screenshot-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .screenshot-image { width: 100%; height: 200px; object-fit: contain; background: #f0f0f0; }
                    .screenshot-info { padding: 15px; }
                    .screenshot-title { font-weight: bold; margin-bottom: 10px; color: #333; }
                    .screenshot-meta { font-size: 12px; color: #666; line-height: 1.4; }
                    .module-section { margin-bottom: 30px; }
                    .module-title { font-size: 20px; font-weight: bold; margin-bottom: 15px; padding: 10px; background: #2196F3; color: white; border-radius: 4px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>$title</h1>
                    <p>生成时间：$timestamp</p>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${screenshots.size}</div>
                        <div>总截图数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${screenshots.groupBy { it.module }.size}</div>
                        <div>模块数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${screenshots.groupBy { it.fragment }.size}</div>
                        <div>页面数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${screenshots.groupBy { it.state }.size}</div>
                        <div>状态数</div>
                    </div>
                </div>
                
                ${generateModuleSections(screenshots)}
            </body>
            </html>
        """.trimIndent()
    }
    
    /**
     * 生成模块部分的HTML
     */
    private fun generateModuleSections(screenshots: List<ScreenshotInfo>): String {
        return screenshots.groupBy { it.module }.map { (module, moduleScreenshots) ->
            """
                <div class="module-section">
                    <div class="module-title">$module (${moduleScreenshots.size}张)</div>
                    <div class="screenshot-grid">
                        ${moduleScreenshots.joinToString("") { screenshot ->
                            """
                                <div class="screenshot-card">
                                    <img src="${screenshot.fileName}" alt="${screenshot.fragment} - ${screenshot.state}" class="screenshot-image" />
                                    <div class="screenshot-info">
                                        <div class="screenshot-title">${screenshot.fragment}</div>
                                        <div class="screenshot-meta">
                                            状态：${screenshot.state}<br>
                                            设备：${screenshot.device}<br>
                                            尺寸：${screenshot.width}x${screenshot.height}<br>
                                            时间：${screenshot.timestamp}
                                        </div>
                                    </div>
                                </div>
                            """.trimIndent()
                        }}
                    </div>
                </div>
            """.trimIndent()
        }.joinToString("\n")
    }
    
    /**
     * 写入文本文件
     */
    fun writeTextFile(file: File, content: String): Boolean {
        return try {
            file.parentFile?.mkdirs()
            file.writeText(content, Charsets.UTF_8)
            LogUtils.debug("写入文本文件成功：${file.absolutePath}")
            true
        } catch (e: Exception) {
            LogUtils.error("写入文本文件失败：${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * 读取文本文件
     */
    fun readTextFile(file: File): String? {
        return try {
            if (file.exists()) {
                file.readText(Charsets.UTF_8)
            } else {
                LogUtils.warning("文本文件不存在：${file.absolutePath}")
                null
            }
        } catch (e: Exception) {
            LogUtils.error("读取文本文件失败：${file.absolutePath}", e)
            null
        }
    }
}

/**
 * 截图信息数据类
 */
data class ScreenshotInfo(
    val fileName: String,
    val module: String,
    val fragment: String,
    val state: String,
    val device: String,
    val width: Int,
    val height: Int,
    val timestamp: String,
    val fileSize: Long
)
