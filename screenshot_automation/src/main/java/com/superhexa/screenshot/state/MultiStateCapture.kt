package com.superhexa.screenshot.state

import android.content.Context
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.core.ScreenshotController
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.utils.LogUtils
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 多状态截图捕获器 - 负责捕获不同界面状态的截图
 * 
 * 主要功能：
 * 1. 状态识别和切换
 * 2. 状态截图捕获
 * 3. 状态恢复管理
 * 4. 状态截图组织
 */
class MultiStateCapture(
    private val context: Context,
    private val config: ScreenshotConfig,
    private val navigationManager: NavigationManager,
    private val stateManager: StateManager,
    private val uiDevice: UiDevice
) {
    
    // 状态截图缓存
    private val stateScreenshots = ConcurrentHashMap<String, List<StateScreenshot>>()
    
    // 状态切换策略
    private val stateStrategies = mapOf(
        "loading" to LoadingStateStrategy(),
        "empty" to EmptyStateStrategy(),
        "error" to ErrorStateStrategy(),
        "success" to SuccessStateStrategy(),
        "offline" to OfflineStateStrategy(),
        "logged_out" to LoggedOutStateStrategy(),
        "no_permission" to NoPermissionStateStrategy(),
        "network_error" to NetworkErrorStateStrategy(),
        "server_error" to ServerErrorStateStrategy(),
        "data_loading" to DataLoadingStateStrategy(),
        "refreshing" to RefreshingStateStrategy()
    )
    
    /**
     * 为指定页面捕获所有状态的截图
     */
    fun captureAllStatesForPage(
        moduleName: String,
        fragmentName: String,
        route: String,
        states: List<String>,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ): List<StateScreenshot> {
        
        LogUtils.info("开始为页面 $fragmentName 捕获 ${states.size} 个状态的截图")
        
        val screenshots = mutableListOf<StateScreenshot>()
        val originalState = getCurrentPageState()
        
        try {
            // 确保在正确的页面
            if (!navigationManager.navigateToFragment(route)) {
                LogUtils.error("无法导航到页面：$fragmentName")
                return screenshots
            }
            
            // 为每个状态捕获截图
            states.forEach { state ->
                val stateScreenshot = captureStateScreenshot(
                    moduleName, fragmentName, state, deviceConfig
                )
                
                if (stateScreenshot != null) {
                    screenshots.add(stateScreenshot)
                    LogUtils.info("状态截图成功：$fragmentName - $state")
                } else {
                    LogUtils.warning("状态截图失败：$fragmentName - $state")
                }
                
                // 状态间隔等待
                Thread.sleep(config.execution.stateChangeTimeout.toLong())
            }
            
            // 缓存截图结果
            val cacheKey = "$moduleName:$fragmentName"
            stateScreenshots[cacheKey] = screenshots
            
        } catch (e: Exception) {
            LogUtils.error("捕获页面状态截图失败：$fragmentName", e)
        } finally {
            // 尝试恢复原始状态
            restorePageState(originalState)
        }
        
        LogUtils.info("页面 $fragmentName 状态截图完成，成功 ${screenshots.size}/${states.size} 个")
        return screenshots
    }
    
    /**
     * 捕获单个状态的截图
     */
    private fun captureStateScreenshot(
        moduleName: String,
        fragmentName: String,
        state: String,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ): StateScreenshot? {
        
        LogUtils.debug("捕获状态截图：$fragmentName - $state")
        
        return try {
            val startTime = System.currentTimeMillis()
            
            // 1. 设置状态
            val stateSetResult = setPageState(state)
            if (!stateSetResult.success) {
                LogUtils.warning("设置状态失败：$state - ${stateSetResult.message}")
                return null
            }
            
            // 2. 等待状态生效
            waitForStateToTakeEffect(state)
            
            // 3. 验证状态
            val stateValid = validateState(state)
            if (!stateValid) {
                LogUtils.warning("状态验证失败：$state")
            }
            
            // 4. 执行截图
            val screenshotFile = takeStateScreenshot(moduleName, fragmentName, state, deviceConfig)
            if (screenshotFile == null) {
                LogUtils.error("截图文件创建失败：$state")
                return null
            }
            
            // 5. 分析状态特征
            val stateFeatures = analyzeStateFeatures(state)
            
            val duration = System.currentTimeMillis() - startTime
            LogUtils.performance("状态截图：$fragmentName - $state", duration)
            
            StateScreenshot(
                moduleName = moduleName,
                fragmentName = fragmentName,
                state = state,
                screenshotFile = screenshotFile,
                deviceConfig = deviceConfig,
                stateFeatures = stateFeatures,
                captureTime = System.currentTimeMillis(),
                valid = stateValid,
                duration = duration
            )
            
        } catch (e: Exception) {
            LogUtils.error("捕获状态截图异常：$fragmentName - $state", e)
            null
        }
    }
    
    /**
     * 设置页面状态
     */
    private fun setPageState(state: String): StateSetResult {
        LogUtils.debug("设置页面状态：$state")
        
        return try {
            // 1. 使用状态策略
            val strategy = stateStrategies[state.lowercase()]
            if (strategy != null) {
                val result = strategy.applyState(context, uiDevice, stateManager)
                if (result.success) {
                    return result
                }
            }
            
            // 2. 使用通用状态管理器
            val success = stateManager.setState(state)
            if (success) {
                StateSetResult.success("状态设置成功")
            } else {
                StateSetResult.failure("状态设置失败")
            }
            
        } catch (e: Exception) {
            LogUtils.error("设置页面状态异常：$state", e)
            StateSetResult.failure("状态设置异常：${e.message}")
        }
    }
    
    /**
     * 等待状态生效
     */
    private fun waitForStateToTakeEffect(state: String) {
        val waitTime = getStateWaitTime(state)
        LogUtils.debug("等待状态生效：$state，等待时间：${waitTime}ms")
        
        Thread.sleep(waitTime)
        
        // 额外等待动画完成
        waitForAnimationsToComplete()
    }
    
    /**
     * 验证状态
     */
    private fun validateState(state: String): Boolean {
        LogUtils.debug("验证状态：$state")
        
        return try {
            when (state.lowercase()) {
                "loading", "加载中" -> validateLoadingState()
                "empty", "空数据" -> validateEmptyState()
                "error", "错误" -> validateErrorState()
                "success", "成功" -> validateSuccessState()
                "offline", "离线" -> validateOfflineState()
                "logged_out", "未登录" -> validateLoggedOutState()
                else -> true // 默认认为有效
            }
        } catch (e: Exception) {
            LogUtils.warning("状态验证异常：$state", e)
            false
        }
    }
    
    /**
     * 分析状态特征
     */
    private fun analyzeStateFeatures(state: String): StateFeatures {
        LogUtils.debug("分析状态特征：$state")
        
        return try {
            val visibleElements = findVisibleElements()
            val clickableElements = findClickableElements()
            val textElements = findTextElements()
            val imageElements = findImageElements()
            
            StateFeatures(
                visibleElementCount = visibleElements.size,
                clickableElementCount = clickableElements.size,
                textElementCount = textElements.size,
                imageElementCount = imageElements.size,
                hasProgressBar = hasProgressBar(),
                hasErrorMessage = hasErrorMessage(),
                hasEmptyView = hasEmptyView(),
                hasLoadingIndicator = hasLoadingIndicator(),
                dominantColors = extractDominantColors(),
                screenBrightness = getScreenBrightness()
            )
        } catch (e: Exception) {
            LogUtils.warning("分析状态特征失败：$state", e)
            StateFeatures()
        }
    }
    
    /**
     * 执行状态截图
     */
    private fun takeStateScreenshot(
        moduleName: String,
        fragmentName: String,
        state: String,
        deviceConfig: ScreenshotConfig.DeviceConfig
    ): File? {
        
        return try {
            // 生成截图文件名
            val timestamp = System.currentTimeMillis()
            val fileName = "${moduleName}_${fragmentName}_${state}_${deviceConfig.name.replace(" ", "_")}_$timestamp.png"
            
            // 创建输出目录
            val outputDir = File(config.output.basePath, "$moduleName/$fragmentName/states")
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            val screenshotFile = File(outputDir, fileName)
            
            // 执行截图
            val success = uiDevice.takeScreenshot(screenshotFile)
            
            if (success && screenshotFile.exists()) {
                LogUtils.screenshot(fileName, true, screenshotFile.length())
                screenshotFile
            } else {
                LogUtils.screenshot(fileName, false)
                null
            }
            
        } catch (e: Exception) {
            LogUtils.error("执行状态截图失败", e)
            null
        }
    }
    
    /**
     * 获取当前页面状态
     */
    private fun getCurrentPageState(): PageState {
        return try {
            PageState(
                activityName = uiDevice.currentActivityName,
                packageName = uiDevice.currentPackageName,
                visibleElements = findVisibleElements().map { it.toString() },
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            LogUtils.warning("获取当前页面状态失败", e)
            PageState()
        }
    }
    
    /**
     * 恢复页面状态
     */
    private fun restorePageState(originalState: PageState) {
        try {
            LogUtils.debug("恢复页面状态")
            
            // 如果当前不在原始页面，尝试返回
            if (uiDevice.currentActivityName != originalState.activityName) {
                navigationManager.goBack()
                Thread.sleep(1000)
            }
            
            // 恢复默认状态
            stateManager.setState("default")
            
        } catch (e: Exception) {
            LogUtils.warning("恢复页面状态失败", e)
        }
    }
    
    // 状态验证方法
    
    private fun validateLoadingState(): Boolean {
        return hasLoadingIndicator() || hasProgressBar()
    }
    
    private fun validateEmptyState(): Boolean {
        return hasEmptyView() || hasEmptyMessage()
    }
    
    private fun validateErrorState(): Boolean {
        return hasErrorMessage() || hasErrorView()
    }
    
    private fun validateSuccessState(): Boolean {
        return !hasErrorMessage() && !hasLoadingIndicator()
    }
    
    private fun validateOfflineState(): Boolean {
        return hasOfflineMessage() || hasNetworkErrorMessage()
    }
    
    private fun validateLoggedOutState(): Boolean {
        return hasLoginButton() || hasLoginPrompt()
    }
    
    // UI元素检测方法
    
    private fun findVisibleElements() = uiDevice.findObjects(androidx.test.uiautomator.By.visible(true))
    private fun findClickableElements() = uiDevice.findObjects(androidx.test.uiautomator.By.clickable(true))
    private fun findTextElements() = uiDevice.findObjects(androidx.test.uiautomator.By.clazz("android.widget.TextView"))
    private fun findImageElements() = uiDevice.findObjects(androidx.test.uiautomator.By.clazz("android.widget.ImageView"))
    
    private fun hasProgressBar(): Boolean {
        return uiDevice.hasObject(androidx.test.uiautomator.By.clazz("android.widget.ProgressBar"))
    }
    
    private fun hasErrorMessage(): Boolean {
        val errorTexts = listOf("错误", "失败", "Error", "Failed", "异常", "Exception")
        return errorTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    private fun hasEmptyView(): Boolean {
        val emptyTexts = listOf("暂无数据", "空", "Empty", "No data", "没有内容")
        return emptyTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    private fun hasLoadingIndicator(): Boolean {
        val loadingTexts = listOf("加载中", "Loading", "请稍候", "Please wait")
        return loadingTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    private fun hasEmptyMessage(): Boolean {
        return hasEmptyView()
    }
    
    private fun hasErrorView(): Boolean {
        return uiDevice.hasObject(androidx.test.uiautomator.By.res(config.project.packageName, "error_view"))
    }
    
    private fun hasOfflineMessage(): Boolean {
        val offlineTexts = listOf("离线", "Offline", "网络不可用", "Network unavailable")
        return offlineTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    private fun hasNetworkErrorMessage(): Boolean {
        val networkErrorTexts = listOf("网络错误", "Network error", "连接失败", "Connection failed")
        return networkErrorTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    private fun hasLoginButton(): Boolean {
        val loginTexts = listOf("登录", "Login", "Sign in")
        return loginTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.text(text).clickable(true))
        }
    }
    
    private fun hasLoginPrompt(): Boolean {
        val loginPromptTexts = listOf("请登录", "Please login", "未登录", "Not logged in")
        return loginPromptTexts.any { text ->
            uiDevice.hasObject(androidx.test.uiautomator.By.textContains(text))
        }
    }
    
    // 辅助方法
    
    private fun getStateWaitTime(state: String): Long {
        return when (state.lowercase()) {
            "loading", "加载中" -> 3000L
            "error", "错误" -> 2000L
            "empty", "空数据" -> 1500L
            "offline", "离线" -> 2000L
            else -> config.execution.stateChangeTimeout.toLong()
        }
    }
    
    private fun waitForAnimationsToComplete() {
        // 等待动画完成
        Thread.sleep(500)
    }
    
    private fun extractDominantColors(): List<String> {
        // 这里可以实现颜色提取逻辑
        return emptyList()
    }
    
    private fun getScreenBrightness(): Float {
        return try {
            android.provider.Settings.System.getInt(
                context.contentResolver,
                android.provider.Settings.System.SCREEN_BRIGHTNESS
            ) / 255.0f
        } catch (e: Exception) {
            0.5f
        }
    }
    
    /**
     * 获取状态截图缓存
     */
    fun getStateScreenshots(moduleName: String, fragmentName: String): List<StateScreenshot>? {
        val cacheKey = "$moduleName:$fragmentName"
        return stateScreenshots[cacheKey]
    }
    
    /**
     * 清理状态截图缓存
     */
    fun clearCache() {
        stateScreenshots.clear()
        LogUtils.info("状态截图缓存已清理")
    }
}

// 数据类定义

data class StateScreenshot(
    val moduleName: String,
    val fragmentName: String,
    val state: String,
    val screenshotFile: File,
    val deviceConfig: ScreenshotConfig.DeviceConfig,
    val stateFeatures: StateFeatures,
    val captureTime: Long,
    val valid: Boolean,
    val duration: Long
)

data class StateFeatures(
    val visibleElementCount: Int = 0,
    val clickableElementCount: Int = 0,
    val textElementCount: Int = 0,
    val imageElementCount: Int = 0,
    val hasProgressBar: Boolean = false,
    val hasErrorMessage: Boolean = false,
    val hasEmptyView: Boolean = false,
    val hasLoadingIndicator: Boolean = false,
    val dominantColors: List<String> = emptyList(),
    val screenBrightness: Float = 0.5f
)

data class PageState(
    val activityName: String = "",
    val packageName: String = "",
    val visibleElements: List<String> = emptyList(),
    val timestamp: Long = 0L
)

sealed class StateSetResult {
    data class Success(val message: String) : StateSetResult()
    data class Failure(val message: String) : StateSetResult()
    
    val success: Boolean get() = this is Success
    val message: String get() = when (this) {
        is Success -> message
        is Failure -> message
    }
    
    companion object {
        fun success(message: String) = Success(message)
        fun failure(message: String) = Failure(message)
    }
}

// 状态策略接口

interface StateStrategy {
    fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult
}

// 具体状态策略实现

class LoadingStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            // 触发加载状态的具体逻辑
            stateManager.setState("loading")
            StateSetResult.success("加载状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("加载状态设置失败：${e.message}")
        }
    }
}

class EmptyStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("empty")
            StateSetResult.success("空数据状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("空数据状态设置失败：${e.message}")
        }
    }
}

class ErrorStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("error")
            StateSetResult.success("错误状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("错误状态设置失败：${e.message}")
        }
    }
}

class SuccessStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("success")
            StateSetResult.success("成功状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("成功状态设置失败：${e.message}")
        }
    }
}

class OfflineStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("offline")
            StateSetResult.success("离线状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("离线状态设置失败：${e.message}")
        }
    }
}

class LoggedOutStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("logged_out")
            StateSetResult.success("未登录状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("未登录状态设置失败：${e.message}")
        }
    }
}

class NoPermissionStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            // 模拟权限不足状态
            StateSetResult.success("无权限状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("无权限状态设置失败：${e.message}")
        }
    }
}

class NetworkErrorStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("offline")
            StateSetResult.success("网络错误状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("网络错误状态设置失败：${e.message}")
        }
    }
}

class ServerErrorStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            // 模拟服务器错误状态
            StateSetResult.success("服务器错误状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("服务器错误状态设置失败：${e.message}")
        }
    }
}

class DataLoadingStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            stateManager.setState("loading")
            StateSetResult.success("数据加载状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("数据加载状态设置失败：${e.message}")
        }
    }
}

class RefreshingStateStrategy : StateStrategy {
    override fun applyState(context: Context, uiDevice: UiDevice, stateManager: StateManager): StateSetResult {
        return try {
            // 触发下拉刷新
            val screenHeight = uiDevice.displayHeight
            val screenWidth = uiDevice.displayWidth
            uiDevice.swipe(screenWidth / 2, screenHeight / 4, screenWidth / 2, screenHeight * 3 / 4, 10)
            
            StateSetResult.success("刷新状态设置成功")
        } catch (e: Exception) {
            StateSetResult.failure("刷新状态设置失败：${e.message}")
        }
    }
}
