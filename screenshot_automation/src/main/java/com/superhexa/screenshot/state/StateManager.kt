package com.superhexa.screenshot.state

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.utils.LogUtils
import java.io.IOException

/**
 * 状态管理器 - 负责模拟不同的应用状态
 * 
 * 主要功能：
 * 1. 数据状态模拟
 * 2. 网络状态控制
 * 3. 用户权限切换
 * 4. 界面状态设置
 */
class StateManager(
    private val context: Context,
    private val config: ScreenshotConfig
) {
    
    private val uiDevice: UiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    private val originalNetworkState = getCurrentNetworkState()
    
    /**
     * 设置指定状态
     */
    fun setState(state: String): Boolean {
        LogUtils.info("设置状态：$state")
        
        return when (state.lowercase()) {
            // 加载状态
            "loading", "加载中" -> setLoadingState()
            
            // 空数据状态
            "empty", "空数据", "无数据" -> setEmptyDataState()
            
            // 错误状态
            "error", "错误", "失败" -> setErrorState()
            
            // 成功状态
            "success", "成功", "完成" -> setSuccessState()
            
            // 网络相关状态
            "offline", "离线" -> setNetworkState(false)
            "online", "在线" -> setNetworkState(true)
            "slow_network", "慢速网络" -> setSlowNetworkState()
            
            // 登录状态
            "logged_in", "已登录" -> setLoginState(true)
            "logged_out", "未登录" -> setLoginState(false)
            
            // 设备连接状态
            "connected", "已连接" -> setDeviceConnectionState(true)
            "disconnected", "未连接" -> setDeviceConnectionState(false)
            
            // 录音状态
            "recording", "录音中" -> setRecordingState(true)
            "not_recording", "未录音" -> setRecordingState(false)
            
            // 播放状态
            "playing", "播放中" -> setPlayingState(true)
            "paused", "暂停" -> setPlayingState(false)
            
            // 编辑状态
            "editing", "编辑中" -> setEditingState(true)
            "preview", "预览" -> setPreviewState()
            "exporting", "导出中" -> setExportingState()
            
            // 搜索状态
            "searching", "搜索中" -> setSearchingState()
            "found_one", "找到一个" -> setFoundDevicesState(1)
            "found_multiple", "找到多个" -> setFoundDevicesState(3)
            
            // 绑定状态
            "bind_success", "绑定成功" -> setBindState(true)
            "bind_failed", "绑定失败" -> setBindState(false)
            
            // 下载状态
            "downloading", "下载中" -> setDownloadingState()
            "downloaded", "已下载" -> setDownloadedState()
            
            // 默认状态
            "default", "initial", "初始" -> setDefaultState()
            
            else -> {
                LogUtils.warning("未知状态：$state")
                setDefaultState()
            }
        }
    }
    
    /**
     * 设置加载状态
     */
    private fun setLoadingState(): Boolean {
        return try {
            // 触发加载状态的方法
            triggerRefresh()
            
            // 等待加载指示器出现
            waitForElement("loading", 2000) ||
            waitForElement("加载中", 2000) ||
            waitForElementById("progress_bar", 2000)
            
        } catch (e: Exception) {
            LogUtils.warning("设置加载状态失败", e)
            false
        }
    }
    
    /**
     * 设置空数据状态
     */
    private fun setEmptyDataState(): Boolean {
        return try {
            // 清除应用数据或使用测试数据
            clearAppData()
            
            // 重启应用以应用空数据状态
            restartApp()
            
            // 等待空状态界面出现
            waitForElement("暂无数据", 3000) ||
            waitForElement("空", 3000) ||
            waitForElementById("empty_view", 3000)
            
        } catch (e: Exception) {
            LogUtils.warning("设置空数据状态失败", e)
            false
        }
    }
    
    /**
     * 设置错误状态
     */
    private fun setErrorState(): Boolean {
        return try {
            // 断开网络连接以触发错误
            setNetworkState(false)
            
            // 触发需要网络的操作
            triggerNetworkOperation()
            
            // 等待错误信息出现
            waitForElement("错误", 3000) ||
            waitForElement("失败", 3000) ||
            waitForElement("网络异常", 3000) ||
            waitForElementById("error_view", 3000)
            
        } catch (e: Exception) {
            LogUtils.warning("设置错误状态失败", e)
            false
        }
    }
    
    /**
     * 设置成功状态
     */
    private fun setSuccessState(): Boolean {
        return try {
            // 确保网络连接正常
            setNetworkState(true)
            
            // 等待成功状态出现
            waitForElement("成功", 2000) ||
            waitForElement("完成", 2000) ||
            waitForElementById("success_view", 2000)
            
        } catch (e: Exception) {
            LogUtils.warning("设置成功状态失败", e)
            true // 默认认为成功
        }
    }
    
    /**
     * 设置网络状态
     */
    private fun setNetworkState(connected: Boolean): Boolean {
        return try {
            if (connected) {
                enableNetwork()
            } else {
                disableNetwork()
            }
            
            // 等待网络状态生效
            Thread.sleep(2000)
            
            LogUtils.debug("设置网络状态：${if (connected) "连接" else "断开"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置网络状态失败", e)
            false
        }
    }
    
    /**
     * 设置慢速网络状态
     */
    private fun setSlowNetworkState(): Boolean {
        return try {
            // 使用adb命令限制网络速度
            executeShellCommand("settings put global netstats_enabled 1")
            
            // 模拟慢速网络（这里只是示例，实际实现可能需要更复杂的网络控制）
            LogUtils.debug("设置慢速网络状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置慢速网络状态失败", e)
            false
        }
    }
    
    /**
     * 设置登录状态
     */
    private fun setLoginState(loggedIn: Boolean): Boolean {
        return try {
            if (loggedIn) {
                // 执行登录操作
                performLogin()
            } else {
                // 执行登出操作
                performLogout()
            }
            
            LogUtils.debug("设置登录状态：${if (loggedIn) "已登录" else "未登录"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置登录状态失败", e)
            false
        }
    }
    
    /**
     * 设置设备连接状态
     */
    private fun setDeviceConnectionState(connected: Boolean): Boolean {
        return try {
            if (connected) {
                // 模拟设备连接
                simulateDeviceConnection()
            } else {
                // 模拟设备断开
                simulateDeviceDisconnection()
            }
            
            LogUtils.debug("设置设备连接状态：${if (connected) "已连接" else "未连接"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置设备连接状态失败", e)
            false
        }
    }
    
    /**
     * 设置录音状态
     */
    private fun setRecordingState(recording: Boolean): Boolean {
        return try {
            if (recording) {
                // 开始录音
                startRecording()
            } else {
                // 停止录音
                stopRecording()
            }
            
            LogUtils.debug("设置录音状态：${if (recording) "录音中" else "未录音"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置录音状态失败", e)
            false
        }
    }
    
    /**
     * 设置播放状态
     */
    private fun setPlayingState(playing: Boolean): Boolean {
        return try {
            if (playing) {
                // 开始播放
                clickElementByText("播放") || clickElementById("play_btn")
            } else {
                // 暂停播放
                clickElementByText("暂停") || clickElementById("pause_btn")
            }
            
            LogUtils.debug("设置播放状态：${if (playing) "播放中" else "暂停"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置播放状态失败", e)
            false
        }
    }
    
    /**
     * 设置编辑状态
     */
    private fun setEditingState(editing: Boolean): Boolean {
        return try {
            if (editing) {
                // 进入编辑模式
                clickElementByText("编辑") || clickElementById("edit_btn")
            }
            
            LogUtils.debug("设置编辑状态：$editing")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置编辑状态失败", e)
            false
        }
    }
    
    /**
     * 设置预览状态
     */
    private fun setPreviewState(): Boolean {
        return try {
            clickElementByText("预览") || clickElementById("preview_btn")
            LogUtils.debug("设置预览状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置预览状态失败", e)
            false
        }
    }
    
    /**
     * 设置导出状态
     */
    private fun setExportingState(): Boolean {
        return try {
            clickElementByText("导出") || 
            clickElementByText("保存") || 
            clickElementById("export_btn")
            
            LogUtils.debug("设置导出状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置导出状态失败", e)
            false
        }
    }
    
    /**
     * 设置搜索状态
     */
    private fun setSearchingState(): Boolean {
        return try {
            clickElementByText("搜索") || 
            clickElementByText("查找设备") || 
            clickElementById("search_btn")
            
            LogUtils.debug("设置搜索状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置搜索状态失败", e)
            false
        }
    }
    
    /**
     * 设置找到设备状态
     */
    private fun setFoundDevicesState(count: Int): Boolean {
        return try {
            // 这里需要根据实际应用的逻辑来模拟找到设备的状态
            // 可能需要修改测试数据或使用mock数据
            
            LogUtils.debug("设置找到设备状态：$count 个设备")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置找到设备状态失败", e)
            false
        }
    }
    
    /**
     * 设置绑定状态
     */
    private fun setBindState(success: Boolean): Boolean {
        return try {
            if (success) {
                // 模拟绑定成功
                waitForElement("绑定成功", 3000) ||
                waitForElement("连接成功", 3000)
            } else {
                // 模拟绑定失败
                waitForElement("绑定失败", 3000) ||
                waitForElement("连接失败", 3000)
            }
            
            LogUtils.debug("设置绑定状态：${if (success) "成功" else "失败"}")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置绑定状态失败", e)
            false
        }
    }
    
    /**
     * 设置下载状态
     */
    private fun setDownloadingState(): Boolean {
        return try {
            clickElementByText("下载") || clickElementById("download_btn")
            
            // 等待下载进度条出现
            waitForElementById("download_progress", 2000)
            
            LogUtils.debug("设置下载状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置下载状态失败", e)
            false
        }
    }
    
    /**
     * 设置已下载状态
     */
    private fun setDownloadedState(): Boolean {
        return try {
            // 等待下载完成状态
            waitForElement("下载完成", 3000) ||
            waitForElement("已下载", 3000)
            
            LogUtils.debug("设置已下载状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置已下载状态失败", e)
            false
        }
    }
    
    /**
     * 设置默认状态
     */
    private fun setDefaultState(): Boolean {
        return try {
            // 重置到默认状态
            restoreNetworkState()
            
            LogUtils.debug("设置默认状态")
            true
        } catch (e: Exception) {
            LogUtils.warning("设置默认状态失败", e)
            false
        }
    }
    
    // 辅助方法
    
    private fun triggerRefresh() {
        try {
            // 下拉刷新
            val screenHeight = uiDevice.displayHeight
            val screenWidth = uiDevice.displayWidth
            uiDevice.swipe(screenWidth / 2, screenHeight / 4, screenWidth / 2, screenHeight * 3 / 4, 10)
        } catch (e: Exception) {
            LogUtils.debug("触发刷新失败", e)
        }
    }
    
    private fun clearAppData() {
        try {
            executeShellCommand("pm clear ${config.project.packageName}")
        } catch (e: Exception) {
            LogUtils.debug("清除应用数据失败", e)
        }
    }
    
    private fun restartApp() {
        try {
            val intent = context.packageManager.getLaunchIntentForPackage(config.project.packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(it)
            }
            Thread.sleep(3000)
        } catch (e: Exception) {
            LogUtils.debug("重启应用失败", e)
        }
    }
    
    private fun triggerNetworkOperation() {
        try {
            triggerRefresh()
        } catch (e: Exception) {
            LogUtils.debug("触发网络操作失败", e)
        }
    }
    
    private fun enableNetwork() {
        try {
            executeShellCommand("svc wifi enable")
            executeShellCommand("svc data enable")
        } catch (e: Exception) {
            LogUtils.debug("启用网络失败", e)
        }
    }
    
    private fun disableNetwork() {
        try {
            executeShellCommand("svc wifi disable")
            executeShellCommand("svc data disable")
        } catch (e: Exception) {
            LogUtils.debug("禁用网络失败", e)
        }
    }
    
    private fun performLogin() {
        // 这里需要根据实际应用的登录流程来实现
        LogUtils.debug("执行登录操作")
    }
    
    private fun performLogout() {
        // 这里需要根据实际应用的登出流程来实现
        LogUtils.debug("执行登出操作")
    }
    
    private fun simulateDeviceConnection() {
        LogUtils.debug("模拟设备连接")
    }
    
    private fun simulateDeviceDisconnection() {
        LogUtils.debug("模拟设备断开")
    }
    
    private fun startRecording() {
        try {
            clickElementByText("录音") || 
            clickElementByText("开始录音") || 
            clickElementById("record_btn")
        } catch (e: Exception) {
            LogUtils.debug("开始录音失败", e)
        }
    }
    
    private fun stopRecording() {
        try {
            clickElementByText("停止") || 
            clickElementByText("停止录音") || 
            clickElementById("stop_btn")
        } catch (e: Exception) {
            LogUtils.debug("停止录音失败", e)
        }
    }
    
    private fun waitForElement(text: String, timeout: Long): Boolean {
        return try {
            uiDevice.wait(androidx.test.uiautomator.Until.hasObject(
                androidx.test.uiautomator.By.textContains(text)
            ), timeout)
        } catch (e: Exception) {
            false
        }
    }
    
    private fun waitForElementById(id: String, timeout: Long): Boolean {
        return try {
            val resourceId = "${config.project.packageName}:id/$id"
            uiDevice.wait(androidx.test.uiautomator.Until.hasObject(
                androidx.test.uiautomator.By.res(resourceId)
            ), timeout)
        } catch (e: Exception) {
            false
        }
    }
    
    private fun clickElementByText(text: String): Boolean {
        return try {
            val element = uiDevice.findObject(UiSelector().text(text))
            if (element.exists()) {
                element.click()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun clickElementById(id: String): Boolean {
        return try {
            val resourceId = "${config.project.packageName}:id/$id"
            val element = uiDevice.findObject(UiSelector().resourceId(resourceId))
            if (element.exists()) {
                element.click()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun executeShellCommand(command: String): String {
        return try {
            uiDevice.executeShellCommand(command)
        } catch (e: IOException) {
            LogUtils.warning("执行Shell命令失败：$command", e)
            ""
        }
    }
    
    private fun getCurrentNetworkState(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        } catch (e: Exception) {
            LogUtils.warning("获取网络状态失败", e)
            true
        }
    }
    
    private fun restoreNetworkState() {
        try {
            if (originalNetworkState) {
                enableNetwork()
            }
        } catch (e: Exception) {
            LogUtils.warning("恢复网络状态失败", e)
        }
    }
}
