package com.superhexa.screenshot.permission

import android.content.Context
import android.content.pm.PackageManager
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.state.StateManager
import com.superhexa.screenshot.utils.LogUtils
import java.io.File

/**
 * 权限差异截图捕获器
 * 
 * 主要功能：
 * 1. 不同用户权限状态截图
 * 2. 权限请求界面截图
 * 3. 权限被拒绝后的界面截图
 * 4. 功能降级界面截图
 */
class PermissionBasedCapture(
    private val context: Context,
    private val config: ScreenshotConfig,
    private val navigationManager: NavigationManager,
    private val stateManager: StateManager,
    private val uiDevice: UiDevice
) {
    
    // 权限截图结果
    private val permissionScreenshots = mutableListOf<PermissionScreenshot>()
    
    // 支持的权限类型
    private val supportedPermissions = listOf(
        PermissionConfig(
            name = "相机权限",
            permission = "android.permission.CAMERA",
            description = "拍照和录像功能",
            relatedFeatures = listOf("拍照", "扫码", "视频录制")
        ),
        PermissionConfig(
            name = "麦克风权限",
            permission = "android.permission.RECORD_AUDIO",
            description = "录音功能",
            relatedFeatures = listOf("语音录制", "语音识别", "通话录音")
        ),
        PermissionConfig(
            name = "存储权限",
            permission = "android.permission.WRITE_EXTERNAL_STORAGE",
            description = "文件读写功能",
            relatedFeatures = listOf("文件保存", "图片导出", "数据备份")
        ),
        PermissionConfig(
            name = "位置权限",
            permission = "android.permission.ACCESS_FINE_LOCATION",
            description = "定位功能",
            relatedFeatures = listOf("地理定位", "附近设备", "位置服务")
        ),
        PermissionConfig(
            name = "蓝牙权限",
            permission = "android.permission.BLUETOOTH",
            description = "蓝牙连接功能",
            relatedFeatures = listOf("设备连接", "数据传输", "音频传输")
        ),
        PermissionConfig(
            name = "通知权限",
            permission = "android.permission.POST_NOTIFICATIONS",
            description = "推送通知功能",
            relatedFeatures = listOf("消息推送", "状态通知", "提醒功能")
        )
    )
    
    // 用户角色配置
    private val userRoles = listOf(
        UserRole(
            name = "游客用户",
            description = "未登录用户，功能受限",
            permissions = emptyList(),
            features = listOf("浏览", "基础功能")
        ),
        UserRole(
            name = "普通用户",
            description = "已登录的普通用户",
            permissions = listOf("CAMERA", "RECORD_AUDIO", "WRITE_EXTERNAL_STORAGE"),
            features = listOf("基础功能", "内容创建", "文件管理")
        ),
        UserRole(
            name = "高级用户",
            description = "具有高级功能权限的用户",
            permissions = listOf("CAMERA", "RECORD_AUDIO", "WRITE_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "BLUETOOTH"),
            features = listOf("全部功能", "高级设置", "设备管理")
        ),
        UserRole(
            name = "管理员",
            description = "具有管理权限的用户",
            permissions = supportedPermissions.map { it.permission.substringAfterLast(".") },
            features = listOf("全部功能", "用户管理", "系统设置")
        )
    )
    
    /**
     * 执行权限差异截图捕获
     */
    fun capturePermissionBasedScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ): List<PermissionScreenshot> {
        
        LogUtils.info("开始权限差异截图捕获：$fragmentName")
        
        try {
            // 1. 捕获不同用户角色的截图
            captureUserRoleScreenshots(moduleName, fragmentName, route)
            
            // 2. 捕获权限请求流程截图
            capturePermissionRequestScreenshots(moduleName, fragmentName, route)
            
            // 3. 捕获权限被拒绝后的截图
            capturePermissionDeniedScreenshots(moduleName, fragmentName, route)
            
            // 4. 捕获功能降级截图
            captureFeatureDegradationScreenshots(moduleName, fragmentName, route)
            
            // 5. 生成权限分析报告
            generatePermissionAnalysisReport(moduleName, fragmentName)
            
            LogUtils.info("权限差异截图捕获完成，共 ${permissionScreenshots.size} 张")
            
        } catch (e: Exception) {
            LogUtils.error("权限差异截图捕获失败：$fragmentName", e)
        }
        
        return permissionScreenshots.toList()
    }
    
    /**
     * 捕获不同用户角色的截图
     */
    private fun captureUserRoleScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获不同用户角色截图")
        
        userRoles.forEach { userRole ->
            try {
                // 设置用户角色状态
                setUserRoleState(userRole)
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 等待界面稳定
                Thread.sleep(2000)
                
                // 执行截图
                val screenshot = takePermissionScreenshot(
                    moduleName, fragmentName, userRole.name, ScreenshotContext.USER_ROLE
                )
                
                if (screenshot != null) {
                    screenshot.userRole = userRole
                    permissionScreenshots.add(screenshot)
                    LogUtils.info("用户角色截图成功：${userRole.name}")
                }
                
            } catch (e: Exception) {
                LogUtils.error("用户角色截图失败：${userRole.name}", e)
            }
        }
    }
    
    /**
     * 捕获权限请求流程截图
     */
    private fun capturePermissionRequestScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获权限请求流程截图")
        
        supportedPermissions.forEach { permissionConfig ->
            try {
                // 撤销权限
                revokePermission(permissionConfig.permission)
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 触发权限请求
                triggerPermissionRequest(permissionConfig)
                
                // 等待权限对话框出现
                Thread.sleep(1000)
                
                // 截图权限请求对话框
                if (isPermissionDialogVisible()) {
                    val screenshot = takePermissionScreenshot(
                        moduleName, fragmentName, "${permissionConfig.name}_请求", ScreenshotContext.PERMISSION_REQUEST
                    )
                    
                    if (screenshot != null) {
                        screenshot.permissionConfig = permissionConfig
                        permissionScreenshots.add(screenshot)
                        LogUtils.info("权限请求截图成功：${permissionConfig.name}")
                    }
                }
                
                // 拒绝权限请求
                denyPermissionRequest()
                
            } catch (e: Exception) {
                LogUtils.error("权限请求截图失败：${permissionConfig.name}", e)
            }
        }
    }
    
    /**
     * 捕获权限被拒绝后的截图
     */
    private fun capturePermissionDeniedScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获权限被拒绝后的截图")
        
        supportedPermissions.forEach { permissionConfig ->
            try {
                // 确保权限被拒绝
                revokePermission(permissionConfig.permission)
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 尝试使用需要权限的功能
                triggerPermissionRequiredFeature(permissionConfig)
                
                // 等待界面响应
                Thread.sleep(2000)
                
                // 截图权限被拒绝后的界面
                val screenshot = takePermissionScreenshot(
                    moduleName, fragmentName, "${permissionConfig.name}_被拒绝", ScreenshotContext.PERMISSION_DENIED
                )
                
                if (screenshot != null) {
                    screenshot.permissionConfig = permissionConfig
                    permissionScreenshots.add(screenshot)
                    LogUtils.info("权限被拒绝截图成功：${permissionConfig.name}")
                }
                
            } catch (e: Exception) {
                LogUtils.error("权限被拒绝截图失败：${permissionConfig.name}", e)
            }
        }
    }
    
    /**
     * 捕获功能降级截图
     */
    private fun captureFeatureDegradationScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获功能降级截图")
        
        // 模拟部分权限被拒绝的场景
        val degradationScenarios = listOf(
            DegradationScenario("无相机权限", listOf("android.permission.CAMERA")),
            DegradationScenario("无存储权限", listOf("android.permission.WRITE_EXTERNAL_STORAGE")),
            DegradationScenario("无位置权限", listOf("android.permission.ACCESS_FINE_LOCATION")),
            DegradationScenario("多权限缺失", listOf("android.permission.CAMERA", "android.permission.RECORD_AUDIO"))
        )
        
        degradationScenarios.forEach { scenario ->
            try {
                // 撤销指定权限
                scenario.revokedPermissions.forEach { permission ->
                    revokePermission(permission)
                }
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 等待界面适应权限变化
                Thread.sleep(2000)
                
                // 截图功能降级后的界面
                val screenshot = takePermissionScreenshot(
                    moduleName, fragmentName, "功能降级_${scenario.name}", ScreenshotContext.FEATURE_DEGRADATION
                )
                
                if (screenshot != null) {
                    screenshot.degradationScenario = scenario
                    permissionScreenshots.add(screenshot)
                    LogUtils.info("功能降级截图成功：${scenario.name}")
                }
                
            } catch (e: Exception) {
                LogUtils.error("功能降级截图失败：${scenario.name}", e)
            }
        }
    }
    
    /**
     * 设置用户角色状态
     */
    private fun setUserRoleState(userRole: UserRole) {
        try {
            when (userRole.name) {
                "游客用户" -> {
                    // 设置为未登录状态
                    stateManager.setState("logged_out")
                    // 撤销所有权限
                    supportedPermissions.forEach { revokePermission(it.permission) }
                }
                "普通用户" -> {
                    // 设置为已登录状态
                    stateManager.setState("logged_in")
                    // 授予基础权限
                    userRole.permissions.forEach { permission ->
                        grantPermission("android.permission.$permission")
                    }
                }
                "高级用户" -> {
                    // 设置为已登录状态
                    stateManager.setState("logged_in")
                    // 授予高级权限
                    userRole.permissions.forEach { permission ->
                        grantPermission("android.permission.$permission")
                    }
                }
                "管理员" -> {
                    // 设置为管理员状态
                    stateManager.setState("admin")
                    // 授予所有权限
                    supportedPermissions.forEach { grantPermission(it.permission) }
                }
            }
            
            LogUtils.debug("用户角色状态设置完成：${userRole.name}")
            
        } catch (e: Exception) {
            LogUtils.error("设置用户角色状态失败：${userRole.name}", e)
        }
    }
    
    /**
     * 触发权限请求
     */
    private fun triggerPermissionRequest(permissionConfig: PermissionConfig) {
        try {
            when (permissionConfig.permission) {
                "android.permission.CAMERA" -> {
                    // 点击拍照按钮触发相机权限请求
                    clickElementByText("拍照") || clickElementByText("相机") || clickElementById("camera_btn")
                }
                "android.permission.RECORD_AUDIO" -> {
                    // 点击录音按钮触发麦克风权限请求
                    clickElementByText("录音") || clickElementByText("语音") || clickElementById("record_btn")
                }
                "android.permission.WRITE_EXTERNAL_STORAGE" -> {
                    // 点击保存按钮触发存储权限请求
                    clickElementByText("保存") || clickElementByText("导出") || clickElementById("save_btn")
                }
                "android.permission.ACCESS_FINE_LOCATION" -> {
                    // 点击定位按钮触发位置权限请求
                    clickElementByText("定位") || clickElementByText("位置") || clickElementById("location_btn")
                }
                "android.permission.BLUETOOTH" -> {
                    // 点击蓝牙连接按钮触发蓝牙权限请求
                    clickElementByText("连接设备") || clickElementByText("蓝牙") || clickElementById("bluetooth_btn")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("触发权限请求失败：${permissionConfig.name}", e)
        }
    }
    
    /**
     * 触发需要权限的功能
     */
    private fun triggerPermissionRequiredFeature(permissionConfig: PermissionConfig) {
        // 与triggerPermissionRequest类似，但重点是触发功能而不是权限对话框
        triggerPermissionRequest(permissionConfig)
    }
    
    /**
     * 检查权限对话框是否可见
     */
    private fun isPermissionDialogVisible(): Boolean {
        return try {
            val permissionTexts = listOf("允许", "拒绝", "权限", "Permission", "Allow", "Deny")
            permissionTexts.any { text ->
                uiDevice.findObject(UiSelector().textContains(text)).exists()
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 拒绝权限请求
     */
    private fun denyPermissionRequest() {
        try {
            val denyTexts = listOf("拒绝", "Deny", "不允许", "Don't allow")
            denyTexts.forEach { text ->
                val element = uiDevice.findObject(UiSelector().text(text))
                if (element.exists()) {
                    element.click()
                    return
                }
            }
        } catch (e: Exception) {
            LogUtils.error("拒绝权限请求失败", e)
        }
    }
    
    /**
     * 授予权限
     */
    private fun grantPermission(permission: String) {
        try {
            uiDevice.executeShellCommand("pm grant ${config.project.packageName} $permission")
            LogUtils.debug("授予权限：$permission")
        } catch (e: Exception) {
            LogUtils.debug("授予权限失败：$permission", e)
        }
    }
    
    /**
     * 撤销权限
     */
    private fun revokePermission(permission: String) {
        try {
            uiDevice.executeShellCommand("pm revoke ${config.project.packageName} $permission")
            LogUtils.debug("撤销权限：$permission")
        } catch (e: Exception) {
            LogUtils.debug("撤销权限失败：$permission", e)
        }
    }
    
    /**
     * 执行权限截图
     */
    private fun takePermissionScreenshot(
        moduleName: String,
        fragmentName: String,
        context: String,
        screenshotContext: ScreenshotContext
    ): PermissionScreenshot? {
        
        return try {
            val timestamp = System.currentTimeMillis()
            val fileName = "${moduleName}_${fragmentName}_permission_${context.replace(" ", "_")}_$timestamp.png"
            
            // 创建输出目录
            val outputDir = File(config.output.basePath, "$moduleName/$fragmentName/permissions")
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            val screenshotFile = File(outputDir, fileName)
            
            // 执行截图
            val success = uiDevice.takeScreenshot(screenshotFile)
            
            if (success && screenshotFile.exists()) {
                // 分析权限相关特征
                val permissionFeatures = analyzePermissionFeatures(screenshotFile, screenshotContext)
                
                // 生成权限元数据
                generatePermissionMetadata(screenshotFile, moduleName, fragmentName, context, screenshotContext, permissionFeatures)
                
                PermissionScreenshot(
                    file = screenshotFile,
                    context = context,
                    screenshotContext = screenshotContext,
                    features = permissionFeatures,
                    timestamp = timestamp
                )
            } else {
                null
            }
            
        } catch (e: Exception) {
            LogUtils.error("权限截图执行失败", e)
            null
        }
    }
    
    /**
     * 分析权限相关特征
     */
    private fun analyzePermissionFeatures(
        screenshotFile: File,
        screenshotContext: ScreenshotContext
    ): PermissionFeatures {
        
        return try {
            PermissionFeatures(
                hasPermissionDialog = isPermissionDialogVisible(),
                hasDisabledFeatures = checkDisabledFeatures(),
                hasPermissionPrompt = checkPermissionPrompt(),
                hasFeatureDegradation = checkFeatureDegradation(screenshotContext),
                visiblePermissionTexts = getVisiblePermissionTexts()
            )
        } catch (e: Exception) {
            LogUtils.error("分析权限特征失败", e)
            PermissionFeatures()
        }
    }
    
    /**
     * 检查禁用的功能
     */
    private fun checkDisabledFeatures(): List<String> {
        val disabledFeatures = mutableListOf<String>()
        
        try {
            // 检查常见的禁用状态指示器
            val disabledIndicators = listOf("禁用", "不可用", "需要权限", "disabled", "unavailable")
            
            disabledIndicators.forEach { indicator ->
                if (uiDevice.findObject(UiSelector().textContains(indicator)).exists()) {
                    disabledFeatures.add(indicator)
                }
            }
        } catch (e: Exception) {
            LogUtils.debug("检查禁用功能失败", e)
        }
        
        return disabledFeatures
    }
    
    /**
     * 检查权限提示
     */
    private fun checkPermissionPrompt(): Boolean {
        return try {
            val promptTexts = listOf("需要权限", "权限不足", "请授予权限", "Permission required")
            promptTexts.any { text ->
                uiDevice.findObject(UiSelector().textContains(text)).exists()
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查功能降级
     */
    private fun checkFeatureDegradation(screenshotContext: ScreenshotContext): Boolean {
        return screenshotContext == ScreenshotContext.FEATURE_DEGRADATION
    }
    
    /**
     * 获取可见的权限相关文本
     */
    private fun getVisiblePermissionTexts(): List<String> {
        val permissionTexts = mutableListOf<String>()
        
        try {
            val keywords = listOf("权限", "允许", "拒绝", "授权", "Permission", "Allow", "Deny", "Grant")
            
            keywords.forEach { keyword ->
                val elements = uiDevice.findObjects(androidx.test.uiautomator.By.textContains(keyword))
                elements.forEach { element ->
                    val text = element.text
                    if (text.isNotEmpty() && !permissionTexts.contains(text)) {
                        permissionTexts.add(text)
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.debug("获取权限文本失败", e)
        }
        
        return permissionTexts
    }
    
    // 辅助方法
    
    private fun clickElementByText(text: String): Boolean {
        return try {
            val element = uiDevice.findObject(UiSelector().text(text))
            if (element.exists()) {
                element.click()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun clickElementById(id: String): Boolean {
        return try {
            val resourceId = "${config.project.packageName}:id/$id"
            val element = uiDevice.findObject(UiSelector().resourceId(resourceId))
            if (element.exists()) {
                element.click()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 生成权限元数据
     */
    private fun generatePermissionMetadata(
        screenshotFile: File,
        moduleName: String,
        fragmentName: String,
        context: String,
        screenshotContext: ScreenshotContext,
        features: PermissionFeatures
    ) {
        val metadata = mapOf(
            "screenshot_file" to screenshotFile.name,
            "module" to moduleName,
            "fragment" to fragmentName,
            "permission_context" to context,
            "screenshot_context" to screenshotContext.name,
            "has_permission_dialog" to features.hasPermissionDialog,
            "has_disabled_features" to features.hasDisabledFeatures,
            "has_permission_prompt" to features.hasPermissionPrompt,
            "has_feature_degradation" to features.hasFeatureDegradation,
            "visible_permission_texts" to features.visiblePermissionTexts,
            "disabled_features" to features.hasDisabledFeatures,
            "timestamp" to java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date()),
            "file_size" to screenshotFile.length()
        )
        
        val metadataFile = File(screenshotFile.parent, "${screenshotFile.nameWithoutExtension}_permission.json")
        com.superhexa.screenshot.utils.FileUtils.writeJson(metadataFile, metadata)
    }
    
    /**
     * 生成权限分析报告
     */
    private fun generatePermissionAnalysisReport(moduleName: String, fragmentName: String) {
        try {
            val reportFile = File(config.output.basePath, "$moduleName/${fragmentName}_permission_analysis.md")
            
            val reportContent = buildString {
                appendLine("# ${fragmentName} 权限差异分析报告")
                appendLine()
                appendLine("## 截图统计")
                appendLine("- 总截图数：${permissionScreenshots.size}")
                appendLine("- 用户角色截图：${permissionScreenshots.count { it.screenshotContext == ScreenshotContext.USER_ROLE }}")
                appendLine("- 权限请求截图：${permissionScreenshots.count { it.screenshotContext == ScreenshotContext.PERMISSION_REQUEST }}")
                appendLine("- 权限被拒绝截图：${permissionScreenshots.count { it.screenshotContext == ScreenshotContext.PERMISSION_DENIED }}")
                appendLine("- 功能降级截图：${permissionScreenshots.count { it.screenshotContext == ScreenshotContext.FEATURE_DEGRADATION }}")
                appendLine()
                
                appendLine("## 权限影响分析")
                supportedPermissions.forEach { permission ->
                    val relatedScreenshots = permissionScreenshots.filter { it.permissionConfig?.permission == permission.permission }
                    if (relatedScreenshots.isNotEmpty()) {
                        appendLine("### ${permission.name}")
                        appendLine("- 相关截图：${relatedScreenshots.size} 张")
                        appendLine("- 影响功能：${permission.relatedFeatures.joinToString(", ")}")
                        appendLine("- 描述：${permission.description}")
                        appendLine()
                    }
                }
                
                appendLine("## 用户体验分析")
                userRoles.forEach { role ->
                    val roleScreenshots = permissionScreenshots.filter { it.userRole?.name == role.name }
                    if (roleScreenshots.isNotEmpty()) {
                        appendLine("### ${role.name}")
                        appendLine("- 可用功能：${role.features.joinToString(", ")}")
                        appendLine("- 权限范围：${role.permissions.joinToString(", ")}")
                        appendLine("- 描述：${role.description}")
                        appendLine()
                    }
                }
            }
            
            reportFile.writeText(reportContent)
            LogUtils.info("权限分析报告生成：${reportFile.absolutePath}")
            
        } catch (e: Exception) {
            LogUtils.error("生成权限分析报告失败", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 恢复所有权限到默认状态
            supportedPermissions.forEach { permission ->
                grantPermission(permission.permission)
            }
            
            permissionScreenshots.clear()
            LogUtils.info("权限截图捕获器清理完成")
        } catch (e: Exception) {
            LogUtils.error("权限截图捕获器清理失败", e)
        }
    }
}

// 数据类定义

data class PermissionConfig(
    val name: String,
    val permission: String,
    val description: String,
    val relatedFeatures: List<String>
)

data class UserRole(
    val name: String,
    val description: String,
    val permissions: List<String>,
    val features: List<String>
)

data class DegradationScenario(
    val name: String,
    val revokedPermissions: List<String>
)

data class PermissionScreenshot(
    val file: File,
    val context: String,
    val screenshotContext: ScreenshotContext,
    val features: PermissionFeatures,
    val timestamp: Long,
    var userRole: UserRole? = null,
    var permissionConfig: PermissionConfig? = null,
    var degradationScenario: DegradationScenario? = null
)

data class PermissionFeatures(
    val hasPermissionDialog: Boolean = false,
    val hasDisabledFeatures: List<String> = emptyList(),
    val hasPermissionPrompt: Boolean = false,
    val hasFeatureDegradation: Boolean = false,
    val visiblePermissionTexts: List<String> = emptyList()
)

enum class ScreenshotContext {
    USER_ROLE,
    PERMISSION_REQUEST,
    PERMISSION_DENIED,
    FEATURE_DEGRADATION
}
