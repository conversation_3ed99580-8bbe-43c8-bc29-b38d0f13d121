package com.superhexa.screenshot.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import java.io.File
import java.io.InputStream

/**
 * 截图配置数据类
 * 
 * 负责加载和管理截图配置信息
 */
data class ScreenshotConfig(
    val version: String,
    val project: ProjectConfig,
    val devices: DevicesConfig,
    val output: OutputConfig,
    val modules: Map<String, ModuleConfig>,
    val userPermissions: List<UserPermissionConfig>,
    val networkStates: List<NetworkStateConfig>,
    val dataStates: List<DataStateConfig>,
    val execution: ExecutionConfig,
    val filters: FiltersConfig
) {
    
    companion object {
        private val mapper = ObjectMapper(YAMLFactory()).apply {
            registerModule(KotlinModule.Builder().build())
        }
        
        /**
         * 从配置文件加载配置
         */
        fun load(configPath: String = "screenshot_automation/config/screenshot_config.yaml"): ScreenshotConfig {
            return try {
                val configFile = File(configPath)
                if (configFile.exists()) {
                    mapper.readValue(configFile)
                } else {
                    // 尝试从资源文件加载
                    val inputStream: InputStream = ScreenshotConfig::class.java.classLoader
                        ?.getResourceAsStream("screenshot_config.yaml")
                        ?: throw IllegalArgumentException("配置文件未找到：$configPath")
                    
                    mapper.readValue(inputStream)
                }
            } catch (e: Exception) {
                throw IllegalArgumentException("加载配置文件失败：${e.message}", e)
            }
        }
        
        /**
         * 从字符串加载配置
         */
        fun loadFromString(yamlContent: String): ScreenshotConfig {
            return mapper.readValue(yamlContent)
        }
    }
    
    /**
     * 项目配置
     */
    data class ProjectConfig(
        val name: String,
        val packageName: String,
        val mainActivity: String
    )
    
    /**
     * 设备配置
     */
    data class DevicesConfig(
        val phone: List<DeviceConfig>,
        val tablet: List<DeviceConfig>
    )
    
    /**
     * 单个设备配置
     */
    data class DeviceConfig(
        val name: String,
        val width: Int,
        val height: Int,
        val density: Double
    )
    
    /**
     * 输出配置
     */
    data class OutputConfig(
        val basePath: String,
        val format: String,
        val quality: Int,
        val namingPattern: String
    )
    
    /**
     * 模块配置
     */
    data class ModuleConfig(
        val name: String,
        val fragments: List<FragmentConfig>
    )
    
    /**
     * Fragment配置
     */
    data class FragmentConfig(
        val name: String,
        val route: String,
        val states: List<String>
    )
    
    /**
     * 用户权限配置
     */
    data class UserPermissionConfig(
        val name: String,
        val loginRequired: Boolean,
        val features: List<String>
    )
    
    /**
     * 网络状态配置
     */
    data class NetworkStateConfig(
        val name: String,
        val connected: Boolean,
        val speed: String? = null
    )
    
    /**
     * 数据状态配置
     */
    data class DataStateConfig(
        val name: String,
        val description: String
    )
    
    /**
     * 执行配置
     */
    data class ExecutionConfig(
        val pageLoadTimeout: Int,
        val stateChangeTimeout: Int,
        val screenshotInterval: Int,
        val retryCount: Int,
        val parallelCount: Int
    )
    
    /**
     * 过滤配置
     */
    data class FiltersConfig(
        val skipPages: List<String>,
        val skipStates: List<String>,
        val includeModules: List<String>,
        val excludeModules: List<String>
    )
    
    /**
     * 验证配置有效性
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        
        // 验证项目配置
        if (project.packageName.isBlank()) {
            errors.add("项目包名不能为空")
        }
        
        if (project.mainActivity.isBlank()) {
            errors.add("主Activity不能为空")
        }
        
        // 验证设备配置
        if (devices.phone.isEmpty() && devices.tablet.isEmpty()) {
            errors.add("至少需要配置一个设备")
        }
        
        devices.phone.forEach { device ->
            if (device.width <= 0 || device.height <= 0) {
                errors.add("设备 ${device.name} 的尺寸配置无效")
            }
        }
        
        devices.tablet.forEach { device ->
            if (device.width <= 0 || device.height <= 0) {
                errors.add("设备 ${device.name} 的尺寸配置无效")
            }
        }
        
        // 验证输出配置
        if (output.basePath.isBlank()) {
            errors.add("输出路径不能为空")
        }
        
        if (output.format !in listOf("png", "jpg", "jpeg")) {
            errors.add("不支持的图片格式：${output.format}")
        }
        
        if (output.quality !in 1..100) {
            errors.add("图片质量必须在1-100之间")
        }
        
        // 验证模块配置
        if (modules.isEmpty()) {
            errors.add("至少需要配置一个模块")
        }
        
        modules.forEach { (moduleName, moduleConfig) ->
            if (moduleConfig.fragments.isEmpty()) {
                errors.add("模块 $moduleName 至少需要配置一个Fragment")
            }
            
            moduleConfig.fragments.forEach { fragment ->
                if (fragment.route.isBlank()) {
                    errors.add("Fragment ${fragment.name} 的路由不能为空")
                }
                
                if (fragment.states.isEmpty()) {
                    errors.add("Fragment ${fragment.name} 至少需要配置一个状态")
                }
            }
        }
        
        // 验证执行配置
        if (execution.pageLoadTimeout <= 0) {
            errors.add("页面加载超时时间必须大于0")
        }
        
        if (execution.stateChangeTimeout <= 0) {
            errors.add("状态切换超时时间必须大于0")
        }
        
        if (execution.screenshotInterval < 0) {
            errors.add("截图间隔时间不能小于0")
        }
        
        if (execution.retryCount < 0) {
            errors.add("重试次数不能小于0")
        }
        
        if (execution.parallelCount <= 0) {
            errors.add("并发执行数量必须大于0")
        }
        
        return errors
    }
    
    /**
     * 获取所有Fragment路由
     */
    fun getAllFragmentRoutes(): List<String> {
        return modules.values.flatMap { module ->
            module.fragments.map { it.route }
        }
    }
    
    /**
     * 获取所有状态
     */
    fun getAllStates(): List<String> {
        return modules.values.flatMap { module ->
            module.fragments.flatMap { it.states }
        }.distinct()
    }
    
    /**
     * 根据路由查找Fragment配置
     */
    fun findFragmentByRoute(route: String): FragmentConfig? {
        return modules.values.flatMap { it.fragments }
            .find { it.route == route }
    }
    
    /**
     * 获取模块的Fragment数量
     */
    fun getModuleFragmentCount(moduleName: String): Int {
        return modules[moduleName]?.fragments?.size ?: 0
    }
    
    /**
     * 计算总截图数量
     */
    fun calculateTotalScreenshots(): Int {
        val deviceCount = devices.phone.size + devices.tablet.size
        val fragmentStateCount = modules.values.sumOf { module ->
            module.fragments.sumOf { it.states.size }
        }
        return deviceCount * fragmentStateCount
    }
    
    /**
     * 获取过滤后的模块
     */
    fun getFilteredModules(): Map<String, ModuleConfig> {
        return when {
            filters.includeModules.isNotEmpty() -> {
                modules.filter { it.key in filters.includeModules }
            }
            filters.excludeModules.isNotEmpty() -> {
                modules.filter { it.key !in filters.excludeModules }
            }
            else -> modules
        }
    }
}
