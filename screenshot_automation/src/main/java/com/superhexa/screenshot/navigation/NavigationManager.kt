package com.superhexa.screenshot.navigation

import android.content.Context
import android.content.Intent
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiSelector
import com.alibaba.android.arouter.launcher.ARouter
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.utils.LogUtils
import java.util.concurrent.TimeUnit

/**
 * 导航管理器 - 负责页面导航和路由跳转
 * 
 * 主要功能：
 * 1. Fragment导航
 * 2. Activity跳转
 * 3. 路由管理
 * 4. 页面状态检测
 */
class NavigationManager(
    private val context: Context,
    private val config: ScreenshotConfig
) {
    
    private val uiDevice: UiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    private val currentRoute = mutableListOf<String>()
    
    init {
        // 初始化ARouter
        try {
            ARouter.init(context.applicationContext)
            LogUtils.info("ARouter初始化成功")
        } catch (e: Exception) {
            LogUtils.warning("ARouter初始化失败，将使用备用导航方式", e)
        }
    }
    
    /**
     * 导航到指定Fragment
     */
    fun navigateToFragment(route: String): Boolean {
        LogUtils.info("导航到Fragment：$route")
        
        return try {
            when {
                // 尝试使用ARouter导航
                tryARouterNavigation(route) -> {
                    currentRoute.add(route)
                    waitForPageLoad()
                    true
                }
                
                // 尝试使用UI自动化导航
                tryUIAutomationNavigation(route) -> {
                    currentRoute.add(route)
                    waitForPageLoad()
                    true
                }
                
                // 尝试使用Intent导航
                tryIntentNavigation(route) -> {
                    currentRoute.add(route)
                    waitForPageLoad()
                    true
                }
                
                else -> {
                    LogUtils.error("无法导航到：$route")
                    false
                }
            }
        } catch (e: Exception) {
            LogUtils.error("导航失败：$route", e)
            false
        }
    }
    
    /**
     * 使用ARouter导航
     */
    private fun tryARouterNavigation(route: String): Boolean {
        return try {
            ARouter.getInstance()
                .build(route)
                .navigation()
            
            LogUtils.info("ARouter导航成功：$route")
            true
        } catch (e: Exception) {
            LogUtils.debug("ARouter导航失败：$route", e)
            false
        }
    }
    
    /**
     * 使用UI自动化导航
     */
    private fun tryUIAutomationNavigation(route: String): Boolean {
        return when {
            // 主页相关导航
            route.contains("/home/") -> navigateToHomeModule(route)
            
            // 登录相关导航
            route.contains("/login/") -> navigateToLoginModule(route)
            
            // 设备相关导航
            route.contains("/device/") -> navigateToDeviceModule(route)
            
            // 音频眼镜相关导航
            route.contains("/audioglasses/") -> navigateToAudioGlassesModule(route)
            
            // 视频编辑相关导航
            route.contains("/videoeditor/") -> navigateToVideoEditorModule(route)
            
            // 用户资料相关导航
            route.contains("/profile/") -> navigateToProfileModule(route)
            
            // 小爱同学相关导航
            route.contains("/xiaoai/") -> navigateToXiaoAiModule(route)
            
            else -> {
                LogUtils.warning("未知的路由模式：$route")
                false
            }
        }
    }
    
    /**
     * 导航到主页模块
     */
    private fun navigateToHomeModule(route: String): Boolean {
        return when {
            route.contains("HomeFragment") -> {
                // 点击底部导航的首页按钮
                clickElementByText("首页") || clickElementById("home_tab")
            }
            
            route.contains("DeviceAddFragment") -> {
                // 先到首页，再点击添加设备
                navigateToHomeModule("/home/<USER>") &&
                (clickElementByText("添加设备") || clickElementById("add_device_btn"))
            }
            
            route.contains("MaterialFragment") -> {
                // 先到首页，再点击素材
                navigateToHomeModule("/home/<USER>") &&
                (clickElementByText("素材") || clickElementById("material_tab"))
            }
            
            route.contains("TemplateListFragment") -> {
                // 先到首页，再点击模板
                navigateToHomeModule("/home/<USER>") &&
                (clickElementByText("模板") || clickElementById("template_tab"))
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到登录模块
     */
    private fun navigateToLoginModule(route: String): Boolean {
        return when {
            route.contains("LoginFragment") -> {
                // 点击登录按钮或个人中心
                clickElementByText("登录") || 
                clickElementByText("个人中心") ||
                clickElementById("login_btn")
            }
            
            route.contains("LoginAccessFragment") -> {
                // 小米账号登录
                navigateToLoginModule("/login/LoginFragment") &&
                (clickElementByText("小米账号登录") || clickElementById("xiaomi_login_btn"))
            }
            
            route.contains("ForgotPasswordFragment") -> {
                // 忘记密码
                navigateToLoginModule("/login/LoginFragment") &&
                (clickElementByText("忘记密码") || clickElementById("forgot_password_btn"))
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到设备模块
     */
    private fun navigateToDeviceModule(route: String): Boolean {
        return when {
            route.contains("DeviceListFragment") -> {
                // 点击设备管理
                clickElementByText("设备管理") || 
                clickElementByText("我的设备") ||
                clickElementById("device_tab")
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到音频眼镜模块
     */
    private fun navigateToAudioGlassesModule(route: String): Boolean {
        return when {
            route.contains("SSHomeFragment") || 
            route.contains("SS2HomeFragment") || 
            route.contains("SSSHomeFragment") -> {
                // 需要先连接音频眼镜设备
                navigateToDeviceModule("/device/DeviceListFragment") &&
                (clickElementByText("音频眼镜") || clickElementByText("智能眼镜"))
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到视频编辑模块
     */
    private fun navigateToVideoEditorModule(route: String): Boolean {
        return when {
            route.contains("VideoEditorFragment") -> {
                clickElementByText("视频编辑") || 
                clickElementByText("编辑") ||
                clickElementById("video_editor_tab")
            }
            
            route.contains("FileExplorerFragment") -> {
                navigateToVideoEditorModule("/videoeditor/VideoEditorFragment") &&
                (clickElementByText("文件管理") || clickElementById("file_explorer_btn"))
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到用户资料模块
     */
    private fun navigateToProfileModule(route: String): Boolean {
        return when {
            route.contains("ProfileFragment") -> {
                clickElementByText("我的") || 
                clickElementByText("个人中心") ||
                clickElementById("profile_tab")
            }
            
            route.contains("SettingsFragment") -> {
                navigateToProfileModule("/profile/ProfileFragment") &&
                (clickElementByText("设置") || clickElementById("settings_btn"))
            }
            
            else -> false
        }
    }
    
    /**
     * 导航到小爱同学模块
     */
    private fun navigateToXiaoAiModule(route: String): Boolean {
        return when {
            route.contains("XiaoAiFragment") -> {
                clickElementByText("小爱同学") || 
                clickElementByText("语音助手") ||
                clickElementById("xiaoai_tab")
            }
            
            else -> false
        }
    }
    
    /**
     * 使用Intent导航
     */
    private fun tryIntentNavigation(route: String): Boolean {
        return try {
            val intent = Intent().apply {
                setClassName(config.project.packageName, getActivityClassForRoute(route))
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            context.startActivity(intent)
            LogUtils.info("Intent导航成功：$route")
            true
        } catch (e: Exception) {
            LogUtils.debug("Intent导航失败：$route", e)
            false
        }
    }
    
    /**
     * 根据路由获取Activity类名
     */
    private fun getActivityClassForRoute(route: String): String {
        return when {
            route.contains("/app/") -> config.project.mainActivity
            else -> config.project.mainActivity
        }
    }
    
    /**
     * 点击指定文本的元素
     */
    private fun clickElementByText(text: String): Boolean {
        return try {
            val element = uiDevice.findObject(UiSelector().text(text))
            if (element.exists()) {
                element.click()
                LogUtils.debug("点击文本元素成功：$text")
                true
            } else {
                // 尝试包含文本的元素
                val containsElement = uiDevice.findObject(UiSelector().textContains(text))
                if (containsElement.exists()) {
                    containsElement.click()
                    LogUtils.debug("点击包含文本元素成功：$text")
                    true
                } else {
                    LogUtils.debug("未找到文本元素：$text")
                    false
                }
            }
        } catch (e: Exception) {
            LogUtils.debug("点击文本元素失败：$text", e)
            false
        }
    }
    
    /**
     * 点击指定ID的元素
     */
    private fun clickElementById(id: String): Boolean {
        return try {
            val resourceId = "${config.project.packageName}:id/$id"
            val element = uiDevice.findObject(UiSelector().resourceId(resourceId))
            if (element.exists()) {
                element.click()
                LogUtils.debug("点击ID元素成功：$id")
                true
            } else {
                LogUtils.debug("未找到ID元素：$id")
                false
            }
        } catch (e: Exception) {
            LogUtils.debug("点击ID元素失败：$id", e)
            false
        }
    }
    
    /**
     * 等待页面加载完成
     */
    private fun waitForPageLoad(): Boolean {
        return try {
            // 等待页面稳定
            Thread.sleep(config.execution.pageLoadTimeout.toLong())
            
            // 检查是否有加载指示器
            val loadingIndicators = listOf("loading", "加载中", "请稍候")
            var isLoading = true
            var waitTime = 0
            val maxWaitTime = config.execution.pageLoadTimeout * 2
            
            while (isLoading && waitTime < maxWaitTime) {
                isLoading = loadingIndicators.any { indicator ->
                    uiDevice.findObject(UiSelector().textContains(indicator)).exists()
                }
                
                if (isLoading) {
                    Thread.sleep(500)
                    waitTime += 500
                }
            }
            
            LogUtils.debug("页面加载完成，等待时间：${waitTime}ms")
            true
        } catch (e: Exception) {
            LogUtils.warning("等待页面加载时发生错误", e)
            false
        }
    }
    
    /**
     * 返回上一页
     */
    fun goBack(): Boolean {
        return try {
            if (currentRoute.isNotEmpty()) {
                currentRoute.removeLastOrNull()
            }
            
            uiDevice.pressBack()
            Thread.sleep(1000)
            
            LogUtils.debug("返回上一页成功")
            true
        } catch (e: Exception) {
            LogUtils.error("返回上一页失败", e)
            false
        }
    }
    
    /**
     * 返回首页
     */
    fun goHome(): Boolean {
        return try {
            currentRoute.clear()
            
            // 多次按返回键直到到达首页
            repeat(5) {
                uiDevice.pressBack()
                Thread.sleep(500)
            }
            
            // 或者直接启动应用
            val intent = context.packageManager.getLaunchIntentForPackage(config.project.packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                context.startActivity(it)
            }
            
            Thread.sleep(2000)
            LogUtils.debug("返回首页成功")
            true
        } catch (e: Exception) {
            LogUtils.error("返回首页失败", e)
            false
        }
    }
    
    /**
     * 获取当前路由
     */
    fun getCurrentRoute(): String? {
        return currentRoute.lastOrNull()
    }
    
    /**
     * 检查是否在指定页面
     */
    fun isOnPage(route: String): Boolean {
        return getCurrentRoute() == route
    }
}
