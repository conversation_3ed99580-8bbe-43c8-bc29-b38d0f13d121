package com.superhexa.screenshot.responsive

import android.content.Context
import android.content.res.Configuration
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.device.DeviceManager
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.utils.LogUtils
import java.io.File

/**
 * 响应式设计截图捕获器
 * 
 * 主要功能：
 * 1. 多设备尺寸截图
 * 2. 横竖屏方向截图
 * 3. 不同密度截图
 * 4. 响应式布局验证
 */
class ResponsiveScreenshotCapture(
    private val context: Context,
    private val config: ScreenshotConfig,
    private val deviceManager: DeviceManager,
    private val navigationManager: NavigationManager,
    private val uiDevice: UiDevice
) {
    
    // 响应式截图结果
    private val responsiveScreenshots = mutableListOf<ResponsiveScreenshot>()
    
    // 预定义的设备配置
    private val deviceConfigurations = listOf(
        // 手机设备
        DeviceConfiguration("iPhone SE", 375, 667, 2.0f, DeviceType.PHONE),
        DeviceConfiguration("iPhone 12", 390, 844, 3.0f, DeviceType.PHONE),
        DeviceConfiguration("iPhone 12 Pro Max", 428, 926, 3.0f, DeviceType.PHONE),
        DeviceConfiguration("Pixel 5", 393, 851, 2.75f, DeviceType.PHONE),
        DeviceConfiguration("Galaxy S21", 384, 854, 2.75f, DeviceType.PHONE),
        
        // 平板设备
        DeviceConfiguration("iPad", 768, 1024, 2.0f, DeviceType.TABLET),
        DeviceConfiguration("iPad Pro 11", 834, 1194, 2.0f, DeviceType.TABLET),
        DeviceConfiguration("iPad Pro 12.9", 1024, 1366, 2.0f, DeviceType.TABLET),
        DeviceConfiguration("Galaxy Tab S7", 800, 1280, 2.0f, DeviceType.TABLET),
        
        // 折叠屏设备
        DeviceConfiguration("Galaxy Fold (展开)", 768, 1024, 2.25f, DeviceType.FOLDABLE),
        DeviceConfiguration("Galaxy Fold (折叠)", 280, 653, 2.25f, DeviceType.FOLDABLE)
    )
    
    /**
     * 执行响应式截图捕获
     */
    fun captureResponsiveScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ): List<ResponsiveScreenshot> {
        
        LogUtils.info("开始响应式截图捕获：$fragmentName")
        
        try {
            // 1. 捕获不同设备尺寸的截图
            captureDeviceSizeScreenshots(moduleName, fragmentName, route)
            
            // 2. 捕获不同方向的截图
            captureOrientationScreenshots(moduleName, fragmentName, route)
            
            // 3. 捕获不同密度的截图
            captureDensityScreenshots(moduleName, fragmentName, route)
            
            // 4. 生成响应式分析报告
            generateResponsiveAnalysisReport(moduleName, fragmentName)
            
            LogUtils.info("响应式截图捕获完成，共 ${responsiveScreenshots.size} 张")
            
        } catch (e: Exception) {
            LogUtils.error("响应式截图捕获失败：$fragmentName", e)
        }
        
        return responsiveScreenshots.toList()
    }
    
    /**
     * 捕获不同设备尺寸的截图
     */
    private fun captureDeviceSizeScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获不同设备尺寸截图")
        
        deviceConfigurations.forEach { deviceConfig ->
            try {
                // 配置设备尺寸
                val configured = configureDeviceSize(deviceConfig)
                if (!configured) {
                    LogUtils.warning("无法配置设备尺寸：${deviceConfig.name}")
                    return@forEach
                }
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 等待布局稳定
                Thread.sleep(3000)
                
                // 执行截图
                val screenshot = takeResponsiveScreenshot(
                    moduleName, fragmentName, deviceConfig, ScreenshotType.DEVICE_SIZE
                )
                
                if (screenshot != null) {
                    responsiveScreenshots.add(screenshot)
                    LogUtils.info("设备尺寸截图成功：${deviceConfig.name}")
                }
                
            } catch (e: Exception) {
                LogUtils.error("设备尺寸截图失败：${deviceConfig.name}", e)
            }
        }
    }
    
    /**
     * 捕获不同方向的截图
     */
    private fun captureOrientationScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获不同方向截图")
        
        val orientations = listOf(
            OrientationConfig("竖屏", Configuration.ORIENTATION_PORTRAIT, 0),
            OrientationConfig("横屏", Configuration.ORIENTATION_LANDSCAPE, 1),
            OrientationConfig("反向竖屏", Configuration.ORIENTATION_PORTRAIT, 2),
            OrientationConfig("反向横屏", Configuration.ORIENTATION_LANDSCAPE, 3)
        )
        
        orientations.forEach { orientationConfig ->
            try {
                // 设置屏幕方向
                setScreenOrientation(orientationConfig.rotation)
                
                // 等待方向切换完成
                Thread.sleep(2000)
                
                // 导航到页面
                if (!navigationManager.navigateToFragment(route)) {
                    LogUtils.warning("无法导航到页面：$route")
                    return@forEach
                }
                
                // 等待布局适配
                Thread.sleep(2000)
                
                // 创建设备配置
                val currentSize = deviceManager.getCurrentScreenSize()
                val deviceConfig = DeviceConfiguration(
                    name = "当前设备_${orientationConfig.name}",
                    width = currentSize.x,
                    height = currentSize.y,
                    density = context.resources.displayMetrics.density,
                    type = DeviceType.PHONE
                )
                
                // 执行截图
                val screenshot = takeResponsiveScreenshot(
                    moduleName, fragmentName, deviceConfig, ScreenshotType.ORIENTATION
                )
                
                if (screenshot != null) {
                    responsiveScreenshots.add(screenshot)
                    LogUtils.info("方向截图成功：${orientationConfig.name}")
                }
                
            } catch (e: Exception) {
                LogUtils.error("方向截图失败：${orientationConfig.name}", e)
            }
        }
        
        // 恢复默认方向
        setScreenOrientation(0)
    }
    
    /**
     * 捕获不同密度的截图
     */
    private fun captureDensityScreenshots(
        moduleName: String,
        fragmentName: String,
        route: String
    ) {
        LogUtils.info("捕获不同密度截图")
        
        val densities = listOf(1.0f, 1.5f, 2.0f, 2.75f, 3.0f, 4.0f)
        
        densities.forEach { density ->
            try {
                // 设置屏幕密度
                val densityDpi = (density * 160).toInt()
                val result = uiDevice.executeShellCommand("wm density $densityDpi")
                
                if (result.contains("Physical density") || result.isEmpty()) {
                    // 等待密度设置生效
                    Thread.sleep(2000)
                    
                    // 导航到页面
                    if (!navigationManager.navigateToFragment(route)) {
                        LogUtils.warning("无法导航到页面：$route")
                        return@forEach
                    }
                    
                    // 等待布局适配
                    Thread.sleep(2000)
                    
                    // 创建设备配置
                    val currentSize = deviceManager.getCurrentScreenSize()
                    val deviceConfig = DeviceConfiguration(
                        name = "密度_${density}x",
                        width = currentSize.x,
                        height = currentSize.y,
                        density = density,
                        type = DeviceType.PHONE
                    )
                    
                    // 执行截图
                    val screenshot = takeResponsiveScreenshot(
                        moduleName, fragmentName, deviceConfig, ScreenshotType.DENSITY
                    )
                    
                    if (screenshot != null) {
                        responsiveScreenshots.add(screenshot)
                        LogUtils.info("密度截图成功：${density}x")
                    }
                }
                
            } catch (e: Exception) {
                LogUtils.error("密度截图失败：${density}x", e)
            }
        }
        
        // 恢复默认密度
        uiDevice.executeShellCommand("wm density reset")
    }
    
    /**
     * 执行响应式截图
     */
    private fun takeResponsiveScreenshot(
        moduleName: String,
        fragmentName: String,
        deviceConfig: DeviceConfiguration,
        screenshotType: ScreenshotType
    ): ResponsiveScreenshot? {
        
        return try {
            val timestamp = System.currentTimeMillis()
            val typePrefix = when (screenshotType) {
                ScreenshotType.DEVICE_SIZE -> "size"
                ScreenshotType.ORIENTATION -> "orientation"
                ScreenshotType.DENSITY -> "density"
            }
            
            val fileName = "${moduleName}_${fragmentName}_${typePrefix}_${deviceConfig.name.replace(" ", "_")}_$timestamp.png"
            
            // 创建输出目录
            val outputDir = File(config.output.basePath, "$moduleName/$fragmentName/responsive")
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            val screenshotFile = File(outputDir, fileName)
            
            // 执行截图
            val success = uiDevice.takeScreenshot(screenshotFile)
            
            if (success && screenshotFile.exists()) {
                // 分析响应式特征
                val responsiveFeatures = analyzeResponsiveFeatures(screenshotFile, deviceConfig)
                
                // 生成元数据
                generateResponsiveMetadata(screenshotFile, moduleName, fragmentName, deviceConfig, screenshotType, responsiveFeatures)
                
                ResponsiveScreenshot(
                    file = screenshotFile,
                    deviceConfig = deviceConfig,
                    screenshotType = screenshotType,
                    features = responsiveFeatures,
                    timestamp = timestamp
                )
            } else {
                null
            }
            
        } catch (e: Exception) {
            LogUtils.error("响应式截图执行失败", e)
            null
        }
    }
    
    /**
     * 配置设备尺寸
     */
    private fun configureDeviceSize(deviceConfig: DeviceConfiguration): Boolean {
        return try {
            // 使用ADB命令设置屏幕尺寸
            val sizeResult = uiDevice.executeShellCommand("wm size ${deviceConfig.width}x${deviceConfig.height}")
            val densityResult = uiDevice.executeShellCommand("wm density ${(deviceConfig.density * 160).toInt()}")
            
            // 等待设置生效
            Thread.sleep(2000)
            
            LogUtils.debug("设备尺寸配置：${deviceConfig.name} - ${sizeResult.take(50)}")
            true
        } catch (e: Exception) {
            LogUtils.error("配置设备尺寸失败：${deviceConfig.name}", e)
            false
        }
    }
    
    /**
     * 设置屏幕方向
     */
    private fun setScreenOrientation(rotation: Int) {
        try {
            when (rotation) {
                0 -> uiDevice.setOrientationNatural()
                1 -> uiDevice.setOrientationLeft()
                2 -> uiDevice.setOrientationNatural() // 反向竖屏，UI Automator不直接支持
                3 -> uiDevice.setOrientationRight()
            }
            
            // 也可以使用ADB命令
            uiDevice.executeShellCommand("settings put system user_rotation $rotation")
            
        } catch (e: Exception) {
            LogUtils.error("设置屏幕方向失败：$rotation", e)
        }
    }
    
    /**
     * 分析响应式特征
     */
    private fun analyzeResponsiveFeatures(
        screenshotFile: File,
        deviceConfig: DeviceConfiguration
    ): ResponsiveFeatures {
        
        return try {
            // 这里可以实现图像分析逻辑
            // 例如：检测布局是否适配、文字是否清晰、元素是否重叠等
            
            ResponsiveFeatures(
                deviceType = deviceConfig.type,
                screenWidth = deviceConfig.width,
                screenHeight = deviceConfig.height,
                density = deviceConfig.density,
                aspectRatio = deviceConfig.width.toFloat() / deviceConfig.height.toFloat(),
                isLandscape = deviceConfig.width > deviceConfig.height,
                layoutAdaptation = checkLayoutAdaptation(deviceConfig),
                textReadability = checkTextReadability(deviceConfig),
                elementVisibility = checkElementVisibility(deviceConfig)
            )
            
        } catch (e: Exception) {
            LogUtils.error("分析响应式特征失败", e)
            ResponsiveFeatures()
        }
    }
    
    /**
     * 检查布局适配
     */
    private fun checkLayoutAdaptation(deviceConfig: DeviceConfiguration): String {
        return when (deviceConfig.type) {
            DeviceType.PHONE -> if (deviceConfig.width < 400) "小屏适配" else "标准适配"
            DeviceType.TABLET -> "平板适配"
            DeviceType.FOLDABLE -> "折叠屏适配"
        }
    }
    
    /**
     * 检查文字可读性
     */
    private fun checkTextReadability(deviceConfig: DeviceConfiguration): String {
        return when {
            deviceConfig.density < 2.0f -> "低密度显示"
            deviceConfig.density > 3.0f -> "高密度显示"
            else -> "标准密度显示"
        }
    }
    
    /**
     * 检查元素可见性
     */
    private fun checkElementVisibility(deviceConfig: DeviceConfiguration): String {
        return when {
            deviceConfig.width < 360 -> "紧凑布局"
            deviceConfig.width > 800 -> "宽松布局"
            else -> "标准布局"
        }
    }
    
    /**
     * 生成响应式元数据
     */
    private fun generateResponsiveMetadata(
        screenshotFile: File,
        moduleName: String,
        fragmentName: String,
        deviceConfig: DeviceConfiguration,
        screenshotType: ScreenshotType,
        features: ResponsiveFeatures
    ) {
        val metadata = mapOf(
            "screenshot_file" to screenshotFile.name,
            "module" to moduleName,
            "fragment" to fragmentName,
            "screenshot_type" to screenshotType.name,
            "device_name" to deviceConfig.name,
            "device_type" to deviceConfig.type.name,
            "device_width" to deviceConfig.width,
            "device_height" to deviceConfig.height,
            "device_density" to deviceConfig.density,
            "aspect_ratio" to features.aspectRatio,
            "is_landscape" to features.isLandscape,
            "layout_adaptation" to features.layoutAdaptation,
            "text_readability" to features.textReadability,
            "element_visibility" to features.elementVisibility,
            "timestamp" to java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date()),
            "file_size" to screenshotFile.length()
        )
        
        val metadataFile = File(screenshotFile.parent, "${screenshotFile.nameWithoutExtension}_responsive.json")
        com.superhexa.screenshot.utils.FileUtils.writeJson(metadataFile, metadata)
    }
    
    /**
     * 生成响应式分析报告
     */
    private fun generateResponsiveAnalysisReport(moduleName: String, fragmentName: String) {
        try {
            val reportFile = File(config.output.basePath, "$moduleName/${fragmentName}_responsive_analysis.md")
            
            val reportContent = buildString {
                appendLine("# ${fragmentName} 响应式设计分析报告")
                appendLine()
                appendLine("## 截图统计")
                appendLine("- 总截图数：${responsiveScreenshots.size}")
                appendLine("- 设备尺寸截图：${responsiveScreenshots.count { it.screenshotType == ScreenshotType.DEVICE_SIZE }}")
                appendLine("- 方向截图：${responsiveScreenshots.count { it.screenshotType == ScreenshotType.ORIENTATION }}")
                appendLine("- 密度截图：${responsiveScreenshots.count { it.screenshotType == ScreenshotType.DENSITY }}")
                appendLine()
                
                appendLine("## 设备类型分布")
                val deviceTypeGroups = responsiveScreenshots.groupBy { it.deviceConfig.type }
                deviceTypeGroups.forEach { (type, screenshots) ->
                    appendLine("- ${type.name}：${screenshots.size} 张")
                }
                appendLine()
                
                appendLine("## 响应式特征分析")
                responsiveScreenshots.forEach { screenshot ->
                    appendLine("### ${screenshot.deviceConfig.name}")
                    appendLine("- 尺寸：${screenshot.deviceConfig.width} x ${screenshot.deviceConfig.height}")
                    appendLine("- 密度：${screenshot.deviceConfig.density}x")
                    appendLine("- 布局适配：${screenshot.features.layoutAdaptation}")
                    appendLine("- 文字可读性：${screenshot.features.textReadability}")
                    appendLine("- 元素可见性：${screenshot.features.elementVisibility}")
                    appendLine()
                }
            }
            
            reportFile.writeText(reportContent)
            LogUtils.info("响应式分析报告生成：${reportFile.absolutePath}")
            
        } catch (e: Exception) {
            LogUtils.error("生成响应式分析报告失败", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 恢复默认设备配置
            uiDevice.executeShellCommand("wm size reset")
            uiDevice.executeShellCommand("wm density reset")
            uiDevice.setOrientationNatural()
            
            responsiveScreenshots.clear()
            LogUtils.info("响应式截图捕获器清理完成")
        } catch (e: Exception) {
            LogUtils.error("响应式截图捕获器清理失败", e)
        }
    }
}

// 数据类定义

data class DeviceConfiguration(
    val name: String,
    val width: Int,
    val height: Int,
    val density: Float,
    val type: DeviceType
)

data class OrientationConfig(
    val name: String,
    val orientation: Int,
    val rotation: Int
)

data class ResponsiveScreenshot(
    val file: File,
    val deviceConfig: DeviceConfiguration,
    val screenshotType: ScreenshotType,
    val features: ResponsiveFeatures,
    val timestamp: Long
)

data class ResponsiveFeatures(
    val deviceType: DeviceType = DeviceType.PHONE,
    val screenWidth: Int = 0,
    val screenHeight: Int = 0,
    val density: Float = 0f,
    val aspectRatio: Float = 0f,
    val isLandscape: Boolean = false,
    val layoutAdaptation: String = "",
    val textReadability: String = "",
    val elementVisibility: String = ""
)

enum class DeviceType {
    PHONE,
    TABLET,
    FOLDABLE
}

enum class ScreenshotType {
    DEVICE_SIZE,
    ORIENTATION,
    DENSITY
}
