package com.superhexa.screenshot.traversal

import android.content.Context
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiSelector
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.utils.LogUtils
import java.util.concurrent.ConcurrentHashMap

/**
 * 页面遍历器 - 负责自动发现和遍历所有页面
 * 
 * 主要功能：
 * 1. 自动发现页面和组件
 * 2. 智能遍历策略
 * 3. 页面状态检测
 * 4. 遍历路径记录
 */
class PageTraversal(
    private val context: Context,
    private val config: ScreenshotConfig,
    private val navigationManager: NavigationManager,
    private val uiDevice: UiDevice
) {
    
    // 已访问的页面记录
    private val visitedPages = ConcurrentHashMap<String, PageInfo>()
    
    // 待访问的页面队列
    private val pendingPages = mutableListOf<PageTarget>()
    
    // 遍历统计
    private var totalPagesFound = 0
    private var totalPagesVisited = 0
    private var totalComponentsFound = 0
    
    /**
     * 开始自动遍历
     */
    fun startTraversal(): TraversalResult {
        LogUtils.info("开始自动页面遍历")
        
        try {
            // 1. 初始化遍历
            initializeTraversal()
            
            // 2. 发现入口页面
            discoverEntryPages()
            
            // 3. 执行深度遍历
            performDepthFirstTraversal()
            
            // 4. 执行广度遍历补充
            performBreadthFirstTraversal()
            
            // 5. 生成遍历报告
            val result = generateTraversalReport()
            
            LogUtils.info("页面遍历完成，发现 $totalPagesFound 个页面，访问 $totalPagesVisited 个页面，发现 $totalComponentsFound 个组件")
            
            return result
            
        } catch (e: Exception) {
            LogUtils.error("页面遍历失败", e)
            return TraversalResult.failure(e.message ?: "未知错误")
        }
    }
    
    /**
     * 初始化遍历
     */
    private fun initializeTraversal() {
        visitedPages.clear()
        pendingPages.clear()
        totalPagesFound = 0
        totalPagesVisited = 0
        totalComponentsFound = 0
        
        LogUtils.debug("遍历初始化完成")
    }
    
    /**
     * 发现入口页面
     */
    private fun discoverEntryPages() {
        LogUtils.info("发现入口页面")
        
        // 1. 从配置文件中获取已知页面
        config.modules.forEach { (moduleName, moduleConfig) ->
            moduleConfig.fragments.forEach { fragmentConfig ->
                val pageTarget = PageTarget(
                    route = fragmentConfig.route,
                    name = fragmentConfig.name,
                    module = moduleName,
                    priority = getPagePriority(fragmentConfig.name),
                    discoveryMethod = DiscoveryMethod.CONFIG
                )
                
                pendingPages.add(pageTarget)
                totalPagesFound++
            }
        }
        
        // 2. 自动发现底部导航页面
        discoverBottomNavigationPages()
        
        // 3. 自动发现侧边栏页面
        discoverSideMenuPages()
        
        // 4. 自动发现设置页面
        discoverSettingsPages()
        
        LogUtils.info("发现入口页面完成，共 ${pendingPages.size} 个页面")
    }
    
    /**
     * 发现底部导航页面
     */
    private fun discoverBottomNavigationPages() {
        try {
            val bottomNavItems = findBottomNavigationItems()
            bottomNavItems.forEach { item ->
                val pageTarget = PageTarget(
                    route = "/auto_discovered/${item.text}",
                    name = "${item.text}Fragment",
                    module = "auto_discovered",
                    priority = PagePriority.HIGH,
                    discoveryMethod = DiscoveryMethod.BOTTOM_NAV,
                    uiElement = item
                )
                
                if (!pendingPages.any { it.name == pageTarget.name }) {
                    pendingPages.add(pageTarget)
                    totalPagesFound++
                }
            }
            
            LogUtils.debug("发现底部导航页面：${bottomNavItems.size} 个")
        } catch (e: Exception) {
            LogUtils.warning("发现底部导航页面失败", e)
        }
    }
    
    /**
     * 发现侧边栏页面
     */
    private fun discoverSideMenuPages() {
        try {
            // 尝试打开侧边栏
            val menuOpened = openSideMenu()
            if (menuOpened) {
                val menuItems = findMenuItems()
                menuItems.forEach { item ->
                    val pageTarget = PageTarget(
                        route = "/auto_discovered/menu/${item.text}",
                        name = "${item.text}Fragment",
                        module = "auto_discovered",
                        priority = PagePriority.MEDIUM,
                        discoveryMethod = DiscoveryMethod.SIDE_MENU,
                        uiElement = item
                    )
                    
                    if (!pendingPages.any { it.name == pageTarget.name }) {
                        pendingPages.add(pageTarget)
                        totalPagesFound++
                    }
                }
                
                // 关闭侧边栏
                closeSideMenu()
                
                LogUtils.debug("发现侧边栏页面：${menuItems.size} 个")
            }
        } catch (e: Exception) {
            LogUtils.warning("发现侧边栏页面失败", e)
        }
    }
    
    /**
     * 发现设置页面
     */
    private fun discoverSettingsPages() {
        try {
            val settingsItems = findSettingsItems()
            settingsItems.forEach { item ->
                val pageTarget = PageTarget(
                    route = "/auto_discovered/settings/${item.text}",
                    name = "${item.text}Fragment",
                    module = "auto_discovered",
                    priority = PagePriority.LOW,
                    discoveryMethod = DiscoveryMethod.SETTINGS,
                    uiElement = item
                )
                
                if (!pendingPages.any { it.name == pageTarget.name }) {
                    pendingPages.add(pageTarget)
                    totalPagesFound++
                }
            }
            
            LogUtils.debug("发现设置页面：${settingsItems.size} 个")
        } catch (e: Exception) {
            LogUtils.warning("发现设置页面失败", e)
        }
    }
    
    /**
     * 执行深度优先遍历
     */
    private fun performDepthFirstTraversal() {
        LogUtils.info("开始深度优先遍历")
        
        // 按优先级排序
        val sortedPages = pendingPages.sortedByDescending { it.priority.value }
        
        sortedPages.forEach { pageTarget ->
            if (!isPageVisited(pageTarget.route)) {
                visitPage(pageTarget)
            }
        }
        
        LogUtils.info("深度优先遍历完成")
    }
    
    /**
     * 执行广度优先遍历补充
     */
    private fun performBreadthFirstTraversal() {
        LogUtils.info("开始广度优先遍历补充")
        
        // 在每个已访问页面中寻找新的可点击元素
        visitedPages.values.forEach { pageInfo ->
            try {
                // 导航到页面
                if (navigationManager.navigateToFragment(pageInfo.route)) {
                    // 发现页面中的可点击元素
                    val clickableElements = discoverClickableElements()
                    
                    clickableElements.forEach { element ->
                        if (shouldExploreElement(element)) {
                            exploreClickableElement(element, pageInfo)
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtils.warning("广度遍历页面失败：${pageInfo.name}", e)
            }
        }
        
        LogUtils.info("广度优先遍历补充完成")
    }
    
    /**
     * 访问页面
     */
    private fun visitPage(pageTarget: PageTarget) {
        LogUtils.info("访问页面：${pageTarget.name}")
        
        try {
            val startTime = System.currentTimeMillis()
            
            // 导航到页面
            val navigated = when (pageTarget.discoveryMethod) {
                DiscoveryMethod.CONFIG -> navigationManager.navigateToFragment(pageTarget.route)
                DiscoveryMethod.BOTTOM_NAV, DiscoveryMethod.SIDE_MENU, DiscoveryMethod.SETTINGS -> {
                    pageTarget.uiElement?.click() ?: false
                }
                DiscoveryMethod.CLICKABLE_ELEMENT -> {
                    pageTarget.uiElement?.click() ?: false
                }
            }
            
            if (!navigated) {
                LogUtils.warning("无法导航到页面：${pageTarget.name}")
                return
            }
            
            // 等待页面加载
            Thread.sleep(config.execution.pageLoadTimeout.toLong())
            
            // 分析页面内容
            val pageInfo = analyzePage(pageTarget)
            
            // 记录访问信息
            visitedPages[pageTarget.route] = pageInfo
            totalPagesVisited++
            
            val duration = System.currentTimeMillis() - startTime
            LogUtils.performance("访问页面：${pageTarget.name}", duration)
            
        } catch (e: Exception) {
            LogUtils.error("访问页面失败：${pageTarget.name}", e)
        }
    }
    
    /**
     * 分析页面内容
     */
    private fun analyzePage(pageTarget: PageTarget): PageInfo {
        val components = mutableListOf<ComponentInfo>()
        
        try {
            // 发现页面组件
            val uiComponents = discoverPageComponents()
            
            uiComponents.forEach { component ->
                val componentInfo = ComponentInfo(
                    type = component.className ?: "Unknown",
                    text = component.text ?: "",
                    resourceId = component.resourceId ?: "",
                    bounds = component.bounds?.toString() ?: "",
                    clickable = component.isClickable,
                    visible = component.isVisible
                )
                
                components.add(componentInfo)
                totalComponentsFound++
            }
            
            LogUtils.debug("页面 ${pageTarget.name} 发现 ${components.size} 个组件")
            
        } catch (e: Exception) {
            LogUtils.warning("分析页面内容失败：${pageTarget.name}", e)
        }
        
        return PageInfo(
            route = pageTarget.route,
            name = pageTarget.name,
            module = pageTarget.module,
            components = components,
            visitTime = System.currentTimeMillis(),
            discoveryMethod = pageTarget.discoveryMethod
        )
    }
    
    /**
     * 发现页面组件
     */
    private fun discoverPageComponents(): List<UiObject> {
        val components = mutableListOf<UiObject>()
        
        try {
            // 查找所有可见的UI元素
            val allElements = uiDevice.findObjects(UiSelector())
            
            allElements.forEach { element ->
                if (element.exists() && element.isVisible) {
                    components.add(element)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.warning("发现页面组件失败", e)
        }
        
        return components
    }
    
    /**
     * 发现可点击元素
     */
    private fun discoverClickableElements(): List<UiObject> {
        val clickableElements = mutableListOf<UiObject>()
        
        try {
            // 查找所有可点击的元素
            val elements = uiDevice.findObjects(UiSelector().clickable(true))
            
            elements.forEach { element ->
                if (element.exists() && element.isVisible && isValidClickableElement(element)) {
                    clickableElements.add(element)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.warning("发现可点击元素失败", e)
        }
        
        return clickableElements
    }
    
    /**
     * 探索可点击元素
     */
    private fun exploreClickableElement(element: UiObject, parentPage: PageInfo) {
        try {
            val elementText = element.text ?: element.contentDescription ?: "Unknown"
            LogUtils.debug("探索可点击元素：$elementText")
            
            // 记录当前页面状态
            val currentPageState = getCurrentPageState()
            
            // 点击元素
            element.click()
            
            // 等待页面变化
            Thread.sleep(2000)
            
            // 检查是否进入了新页面
            val newPageState = getCurrentPageState()
            
            if (newPageState != currentPageState) {
                // 发现新页面
                val newPageTarget = PageTarget(
                    route = "/auto_discovered/clickable/${elementText}",
                    name = "${elementText}Fragment",
                    module = parentPage.module,
                    priority = PagePriority.LOW,
                    discoveryMethod = DiscoveryMethod.CLICKABLE_ELEMENT,
                    parentPage = parentPage.route
                )
                
                if (!isPageVisited(newPageTarget.route)) {
                    visitPage(newPageTarget)
                }
            }
            
            // 返回原页面
            navigationManager.goBack()
            Thread.sleep(1000)
            
        } catch (e: Exception) {
            LogUtils.warning("探索可点击元素失败", e)
        }
    }
    
    // 辅助方法
    
    private fun findBottomNavigationItems(): List<UiObject> {
        val items = mutableListOf<UiObject>()
        
        try {
            // 查找底部导航相关的元素
            val bottomNavSelectors = listOf(
                UiSelector().resourceIdMatches(".*tab.*"),
                UiSelector().resourceIdMatches(".*navigation.*"),
                UiSelector().resourceIdMatches(".*bottom.*"),
                UiSelector().className("android.widget.TabWidget"),
                UiSelector().className("com.google.android.material.bottomnavigation.BottomNavigationView")
            )
            
            bottomNavSelectors.forEach { selector ->
                val elements = uiDevice.findObjects(selector)
                elements.forEach { element ->
                    if (element.exists() && element.isVisible && element.isClickable) {
                        items.add(element)
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtils.debug("查找底部导航项失败", e)
        }
        
        return items.distinctBy { it.text ?: it.contentDescription }
    }
    
    private fun findMenuItems(): List<UiObject> {
        val items = mutableListOf<UiObject>()
        
        try {
            // 查找菜单项
            val menuSelectors = listOf(
                UiSelector().resourceIdMatches(".*menu.*"),
                UiSelector().className("android.widget.ListView"),
                UiSelector().className("androidx.recyclerview.widget.RecyclerView")
            )
            
            menuSelectors.forEach { selector ->
                val elements = uiDevice.findObjects(selector)
                elements.forEach { element ->
                    if (element.exists() && element.isVisible && element.isClickable) {
                        items.add(element)
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtils.debug("查找菜单项失败", e)
        }
        
        return items
    }
    
    private fun findSettingsItems(): List<UiObject> {
        val items = mutableListOf<UiObject>()
        
        try {
            // 查找设置相关的元素
            val settingsTexts = listOf("设置", "Settings", "选项", "Options", "配置", "Config")
            
            settingsTexts.forEach { text ->
                val element = uiDevice.findObject(UiSelector().textContains(text).clickable(true))
                if (element.exists()) {
                    items.add(element)
                }
            }
            
        } catch (e: Exception) {
            LogUtils.debug("查找设置项失败", e)
        }
        
        return items
    }
    
    private fun openSideMenu(): Boolean {
        return try {
            // 尝试多种方式打开侧边栏
            val menuButton = uiDevice.findObject(UiSelector().descriptionContains("menu")) ?:
                             uiDevice.findObject(UiSelector().descriptionContains("导航")) ?:
                             uiDevice.findObject(UiSelector().resourceIdMatches(".*menu.*"))
            
            if (menuButton.exists()) {
                menuButton.click()
                Thread.sleep(1000)
                true
            } else {
                // 尝试从左边缘滑动
                val screenHeight = uiDevice.displayHeight
                uiDevice.swipe(0, screenHeight / 2, 200, screenHeight / 2, 10)
                Thread.sleep(1000)
                true
            }
        } catch (e: Exception) {
            LogUtils.debug("打开侧边栏失败", e)
            false
        }
    }
    
    private fun closeSideMenu(): Boolean {
        return try {
            uiDevice.pressBack()
            Thread.sleep(500)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun isPageVisited(route: String): Boolean {
        return visitedPages.containsKey(route)
    }
    
    private fun getPagePriority(pageName: String): PagePriority {
        return when {
            pageName.contains("Home", ignoreCase = true) -> PagePriority.HIGH
            pageName.contains("Main", ignoreCase = true) -> PagePriority.HIGH
            pageName.contains("Login", ignoreCase = true) -> PagePriority.HIGH
            pageName.contains("Settings", ignoreCase = true) -> PagePriority.LOW
            pageName.contains("About", ignoreCase = true) -> PagePriority.LOW
            else -> PagePriority.MEDIUM
        }
    }
    
    private fun shouldExploreElement(element: UiObject): Boolean {
        return try {
            val text = element.text ?: element.contentDescription ?: ""
            
            // 跳过一些不需要探索的元素
            val skipTexts = listOf("返回", "Back", "取消", "Cancel", "关闭", "Close")
            
            !skipTexts.any { text.contains(it, ignoreCase = true) }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun isValidClickableElement(element: UiObject): Boolean {
        return try {
            val text = element.text ?: element.contentDescription ?: ""
            val className = element.className ?: ""
            
            // 有效的可点击元素类型
            val validClasses = listOf(
                "android.widget.Button",
                "android.widget.ImageButton",
                "android.widget.TextView",
                "androidx.appcompat.widget.AppCompatButton"
            )
            
            text.isNotEmpty() || validClasses.any { className.contains(it) }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun getCurrentPageState(): String {
        return try {
            // 获取当前页面的唯一标识
            val currentActivity = uiDevice.currentActivityName
            val visibleElements = uiDevice.findObjects(UiSelector().visible(true))
            val elementCount = visibleElements.size
            
            "$currentActivity:$elementCount"
        } catch (e: Exception) {
            "unknown:${System.currentTimeMillis()}"
        }
    }
    
    private fun generateTraversalReport(): TraversalResult {
        val report = TraversalReport(
            totalPagesFound = totalPagesFound,
            totalPagesVisited = totalPagesVisited,
            totalComponentsFound = totalComponentsFound,
            visitedPages = visitedPages.values.toList(),
            traversalTime = System.currentTimeMillis()
        )
        
        return TraversalResult.success(report)
    }
}

// 数据类定义

data class PageTarget(
    val route: String,
    val name: String,
    val module: String,
    val priority: PagePriority,
    val discoveryMethod: DiscoveryMethod,
    val uiElement: UiObject? = null,
    val parentPage: String? = null
)

data class PageInfo(
    val route: String,
    val name: String,
    val module: String,
    val components: List<ComponentInfo>,
    val visitTime: Long,
    val discoveryMethod: DiscoveryMethod
)

data class ComponentInfo(
    val type: String,
    val text: String,
    val resourceId: String,
    val bounds: String,
    val clickable: Boolean,
    val visible: Boolean
)

enum class PagePriority(val value: Int) {
    HIGH(3),
    MEDIUM(2),
    LOW(1)
}

enum class DiscoveryMethod {
    CONFIG,           // 从配置文件发现
    BOTTOM_NAV,       // 从底部导航发现
    SIDE_MENU,        // 从侧边栏发现
    SETTINGS,         // 从设置页面发现
    CLICKABLE_ELEMENT // 从可点击元素发现
}

sealed class TraversalResult {
    data class Success(val report: TraversalReport) : TraversalResult()
    data class Failure(val error: String) : TraversalResult()
    
    companion object {
        fun success(report: TraversalReport) = Success(report)
        fun failure(error: String) = Failure(error)
    }
}

data class TraversalReport(
    val totalPagesFound: Int,
    val totalPagesVisited: Int,
    val totalComponentsFound: Int,
    val visitedPages: List<PageInfo>,
    val traversalTime: Long
)
