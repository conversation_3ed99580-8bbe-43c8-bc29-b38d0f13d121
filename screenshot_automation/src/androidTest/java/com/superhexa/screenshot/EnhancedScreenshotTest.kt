package com.superhexa.screenshot

import android.content.Context
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.core.ScreenshotController
import com.superhexa.screenshot.core.ScreenshotResult
import com.superhexa.screenshot.device.DeviceManager
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.organization.OrganizationStrategy
import com.superhexa.screenshot.organization.ScreenshotOrganizer
import com.superhexa.screenshot.permission.PermissionBasedCapture
import com.superhexa.screenshot.responsive.ResponsiveScreenshotCapture
import com.superhexa.screenshot.state.MultiStateCapture
import com.superhexa.screenshot.state.StateManager
import com.superhexa.screenshot.traversal.PageTraversal
import com.superhexa.screenshot.utils.FileUtils
import com.superhexa.screenshot.utils.LogUtils
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

/**
 * 增强版截图自动化测试
 * 
 * 测试所有新增的高级功能：
 * 1. 响应式设计截图
 * 2. 权限差异截图
 * 3. 截图组织和命名优化
 * 4. 多状态截图
 * 5. 页面遍历
 */
@RunWith(AndroidJUnit4::class)
class EnhancedScreenshotTest {
    
    private lateinit var context: Context
    private lateinit var uiDevice: UiDevice
    private lateinit var config: ScreenshotConfig
    
    // 核心组件
    private lateinit var deviceManager: DeviceManager
    private lateinit var navigationManager: NavigationManager
    private lateinit var stateManager: StateManager
    private lateinit var screenshotController: ScreenshotController
    
    // 高级功能组件
    private lateinit var responsiveCapture: ResponsiveScreenshotCapture
    private lateinit var permissionCapture: PermissionBasedCapture
    private lateinit var multiStateCapture: MultiStateCapture
    private lateinit var pageTraversal: PageTraversal
    private lateinit var screenshotOrganizer: ScreenshotOrganizer
    
    @Before
    fun setUp() {
        // 初始化测试环境
        context = InstrumentationRegistry.getInstrumentation().targetContext
        uiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
        
        // 加载配置
        config = ScreenshotConfig.load()
        
        // 初始化日志系统
        val logDir = File(context.getExternalFilesDir(null), "logs").absolutePath
        LogUtils.init(LogUtils.LogLevel.DEBUG, true, logDir)
        
        // 初始化核心组件
        deviceManager = DeviceManager(uiDevice, config)
        navigationManager = NavigationManager(context, config)
        stateManager = StateManager(context, config)
        screenshotController = ScreenshotController(context)
        
        // 初始化高级功能组件
        responsiveCapture = ResponsiveScreenshotCapture(context, config, deviceManager, navigationManager, uiDevice)
        permissionCapture = PermissionBasedCapture(context, config, navigationManager, stateManager, uiDevice)
        multiStateCapture = MultiStateCapture(context, config, navigationManager, stateManager, uiDevice)
        pageTraversal = PageTraversal(context, config, navigationManager, uiDevice)
        screenshotOrganizer = ScreenshotOrganizer(config)
        
        LogUtils.info("增强版截图测试初始化完成")
        
        // 配置设备环境
        deviceManager.configureSystemSettings()
        
        // 唤醒设备
        uiDevice.wakeUp()
    }
    
    @After
    fun tearDown() {
        try {
            // 清理资源
            screenshotController.cleanup()
            responsiveCapture.cleanup()
            permissionCapture.cleanup()
            multiStateCapture.clearCache()
            
            LogUtils.info("增强版截图测试清理完成")
        } catch (e: Exception) {
            LogUtils.error("测试清理失败", e)
        }
    }
    
    /**
     * 测试完整的增强版截图流程
     */
    @Test
    fun testEnhancedScreenshotProcess() {
        LogUtils.info("开始增强版截图流程测试")
        
        try {
            // 执行完整的截图流程
            val result = screenshotController.startScreenshotProcess()
            
            // 验证结果
            when (result) {
                is ScreenshotResult.Success -> {
                    LogUtils.info("增强版截图流程测试成功")
                    LogUtils.info("截图报告：${result.report}")
                    
                    // 验证截图数量
                    assert(result.report.successfulScreenshots > 0) {
                        "应该有成功的截图"
                    }
                    
                    // 验证文件组织
                    verifyScreenshotOrganization()
                    
                    // 验证响应式截图
                    verifyResponsiveScreenshots()
                    
                    // 验证权限截图
                    verifyPermissionScreenshots()
                }
                
                is ScreenshotResult.Failure -> {
                    LogUtils.error("增强版截图流程测试失败：${result.error}")
                    throw AssertionError("增强版截图流程测试失败：${result.error}")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("增强版截图流程测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试响应式设计截图功能
     */
    @Test
    fun testResponsiveScreenshots() {
        LogUtils.info("开始响应式设计截图测试")
        
        try {
            val testModule = "home"
            val testFragment = "HomeFragment"
            val testRoute = "/home/<USER>"
            
            // 执行响应式截图
            val screenshots = responsiveCapture.captureResponsiveScreenshots(
                testModule, testFragment, testRoute
            )
            
            LogUtils.info("响应式截图测试完成，生成 ${screenshots.size} 张截图")
            
            assert(screenshots.isNotEmpty()) {
                "应该生成响应式截图"
            }
            
            // 验证不同设备类型的截图
            val deviceTypes = screenshots.map { it.deviceConfig.type }.distinct()
            assert(deviceTypes.isNotEmpty()) {
                "应该包含不同设备类型的截图"
            }
            
            // 验证截图文件存在
            screenshots.forEach { screenshot ->
                assert(screenshot.file.exists()) {
                    "响应式截图文件应该存在：${screenshot.file.absolutePath}"
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("响应式设计截图测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试权限差异截图功能
     */
    @Test
    fun testPermissionBasedScreenshots() {
        LogUtils.info("开始权限差异截图测试")
        
        try {
            val testModule = "device"
            val testFragment = "DeviceAddFragment"
            val testRoute = "/device/DeviceAddFragment"
            
            // 执行权限差异截图
            val screenshots = permissionCapture.capturePermissionBasedScreenshots(
                testModule, testFragment, testRoute
            )
            
            LogUtils.info("权限差异截图测试完成，生成 ${screenshots.size} 张截图")
            
            assert(screenshots.isNotEmpty()) {
                "应该生成权限差异截图"
            }
            
            // 验证不同权限上下文的截图
            val contexts = screenshots.map { it.screenshotContext }.distinct()
            assert(contexts.isNotEmpty()) {
                "应该包含不同权限上下文的截图"
            }
            
            // 验证截图文件存在
            screenshots.forEach { screenshot ->
                assert(screenshot.file.exists()) {
                    "权限差异截图文件应该存在：${screenshot.file.absolutePath}"
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("权限差异截图测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试截图组织功能
     */
    @Test
    fun testScreenshotOrganization() {
        LogUtils.info("开始截图组织测试")
        
        try {
            // 创建一些测试截图文件
            val testFiles = createTestScreenshotFiles()
            
            // 执行文件组织
            val result = screenshotOrganizer.organizeScreenshots(
                testFiles, OrganizationStrategy.BY_MODULE
            )
            
            assert(result.success) {
                "截图组织应该成功"
            }
            
            assert(result.processedFiles > 0) {
                "应该处理一些文件"
            }
            
            LogUtils.info("截图组织测试完成：${result.summary}")
            
            // 验证索引文件生成
            val indexFile = File(config.output.basePath, "README.md")
            assert(indexFile.exists()) {
                "应该生成主索引文件"
            }
            
        } catch (e: Exception) {
            LogUtils.error("截图组织测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试多状态截图功能
     */
    @Test
    fun testMultiStateCapture() {
        LogUtils.info("开始多状态截图测试")
        
        try {
            val testModule = "home"
            val testFragment = "HomeFragment"
            val testRoute = "/home/<USER>"
            val testStates = listOf("loading", "empty", "success", "error")
            val testDevice = config.devices.phone.first()
            
            // 执行多状态截图
            val screenshots = multiStateCapture.captureAllStatesForPage(
                testModule, testFragment, testRoute, testStates, testDevice
            )
            
            LogUtils.info("多状态截图测试完成，生成 ${screenshots.size} 张截图")
            
            assert(screenshots.isNotEmpty()) {
                "应该生成多状态截图"
            }
            
            // 验证不同状态的截图
            val states = screenshots.map { it.state }.distinct()
            assert(states.isNotEmpty()) {
                "应该包含不同状态的截图"
            }
            
        } catch (e: Exception) {
            LogUtils.error("多状态截图测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试页面遍历功能
     */
    @Test
    fun testPageTraversal() {
        LogUtils.info("开始页面遍历测试")
        
        try {
            val result = pageTraversal.startTraversal()
            
            when (result) {
                is com.superhexa.screenshot.traversal.TraversalResult.Success -> {
                    LogUtils.info("页面遍历测试成功")
                    LogUtils.info("遍历报告：${result.report}")
                    
                    assert(result.report.totalPagesFound > 0) {
                        "应该发现一些页面"
                    }
                }
                
                is com.superhexa.screenshot.traversal.TraversalResult.Failure -> {
                    LogUtils.warning("页面遍历测试失败：${result.error}")
                    // 页面遍历失败不影响整体测试
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("页面遍历测试异常", e)
            throw e
        }
    }
    
    // 辅助方法
    
    private fun verifyScreenshotOrganization() {
        val outputDir = File(config.output.basePath)
        assert(outputDir.exists()) {
            "输出目录应该存在"
        }
        
        // 验证模块目录结构
        val moduleDirectories = outputDir.listFiles { file -> file.isDirectory }
        assert(moduleDirectories?.isNotEmpty() == true) {
            "应该有模块目录"
        }
        
        LogUtils.info("截图组织验证通过")
    }
    
    private fun verifyResponsiveScreenshots() {
        val responsiveDir = File(config.output.basePath)
        val responsiveFiles = FileUtils.listFiles(responsiveDir, "png", true)
            .filter { it.name.contains("responsive") || it.name.contains("size") || it.name.contains("orientation") }
        
        if (responsiveFiles.isNotEmpty()) {
            LogUtils.info("响应式截图验证通过：${responsiveFiles.size} 张")
        } else {
            LogUtils.warning("未找到响应式截图文件")
        }
    }
    
    private fun verifyPermissionScreenshots() {
        val permissionDir = File(config.output.basePath)
        val permissionFiles = FileUtils.listFiles(permissionDir, "png", true)
            .filter { it.name.contains("permission") }
        
        if (permissionFiles.isNotEmpty()) {
            LogUtils.info("权限截图验证通过：${permissionFiles.size} 张")
        } else {
            LogUtils.warning("未找到权限截图文件")
        }
    }
    
    private fun createTestScreenshotFiles(): List<File> {
        val testFiles = mutableListOf<File>()
        val testDir = File(config.output.basePath, "test")
        testDir.mkdirs()
        
        // 创建一些测试文件
        val testFileNames = listOf(
            "home_HomeFragment_default_phone_20231201_143022.png",
            "device_DeviceAddFragment_loading_tablet_20231201_143025.png",
            "profile_ProfileFragment_success_phone_20231201_143028.png"
        )
        
        testFileNames.forEach { fileName ->
            val testFile = File(testDir, fileName)
            testFile.writeText("test content") // 创建空文件
            testFiles.add(testFile)
        }
        
        return testFiles
    }
}
