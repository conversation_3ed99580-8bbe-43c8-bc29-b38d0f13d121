package com.superhexa.screenshot

import android.content.Context
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.superhexa.screenshot.config.ScreenshotConfig
import com.superhexa.screenshot.core.ScreenshotController
import com.superhexa.screenshot.core.ScreenshotResult
import com.superhexa.screenshot.device.DeviceManager
import com.superhexa.screenshot.navigation.NavigationManager
import com.superhexa.screenshot.state.MultiStateCapture
import com.superhexa.screenshot.state.StateManager
import com.superhexa.screenshot.traversal.PageTraversal
import com.superhexa.screenshot.utils.FileUtils
import com.superhexa.screenshot.utils.LogUtils
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

/**
 * 昆明项目自动化截图测试类
 * 
 * 这是主要的测试入口，负责执行完整的截图流程
 */
@RunWith(AndroidJUnit4::class)
class ScreenshotAutomationTest {
    
    private lateinit var context: Context
    private lateinit var uiDevice: UiDevice
    private lateinit var config: ScreenshotConfig
    
    // 核心组件
    private lateinit var deviceManager: DeviceManager
    private lateinit var navigationManager: NavigationManager
    private lateinit var stateManager: StateManager
    private lateinit var multiStateCapture: MultiStateCapture
    private lateinit var pageTraversal: PageTraversal
    private lateinit var screenshotController: ScreenshotController
    
    @Before
    fun setUp() {
        // 初始化测试环境
        context = InstrumentationRegistry.getInstrumentation().targetContext
        uiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
        
        // 加载配置
        config = ScreenshotConfig.load()
        
        // 初始化日志系统
        val logDir = File(context.getExternalFilesDir(null), "logs").absolutePath
        LogUtils.init(LogUtils.LogLevel.DEBUG, true, logDir)
        
        // 初始化核心组件
        deviceManager = DeviceManager(uiDevice, config)
        navigationManager = NavigationManager(context, config)
        stateManager = StateManager(context, config)
        multiStateCapture = MultiStateCapture(context, config, navigationManager, stateManager, uiDevice)
        pageTraversal = PageTraversal(context, config, navigationManager, uiDevice)
        screenshotController = ScreenshotController(context)
        
        LogUtils.info("截图自动化测试初始化完成")
        
        // 配置设备环境
        deviceManager.configureSystemSettings()
        
        // 唤醒设备
        uiDevice.wakeUp()
        
        // 解锁屏幕（如果需要）
        unlockScreen()
    }
    
    @After
    fun tearDown() {
        try {
            // 清理资源
            deviceManager.cleanup()
            multiStateCapture.clearCache()
            
            LogUtils.info("截图自动化测试清理完成")
        } catch (e: Exception) {
            LogUtils.error("测试清理失败", e)
        }
    }
    
    /**
     * 完整的截图自动化测试
     */
    @Test
    fun testCompleteScreenshotAutomation() {
        LogUtils.info("开始完整的截图自动化测试")
        
        try {
            // 验证配置
            validateConfiguration()
            
            // 执行截图流程
            val result = screenshotController.startScreenshotProcess()
            
            // 验证结果
            when (result) {
                is ScreenshotResult.Success -> {
                    LogUtils.info("截图自动化测试成功完成")
                    LogUtils.info("截图报告：${result.report}")
                    
                    // 生成HTML报告
                    generateHtmlReport(result.report)
                    
                    assert(result.report.successfulScreenshots > 0) {
                        "至少应该有一张成功的截图"
                    }
                }
                
                is ScreenshotResult.Failure -> {
                    LogUtils.error("截图自动化测试失败：${result.error}")
                    throw AssertionError("截图自动化测试失败：${result.error}")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("截图自动化测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试页面遍历功能
     */
    @Test
    fun testPageTraversal() {
        LogUtils.info("开始页面遍历测试")
        
        try {
            val result = pageTraversal.startTraversal()
            
            when (result) {
                is com.superhexa.screenshot.traversal.TraversalResult.Success -> {
                    LogUtils.info("页面遍历测试成功")
                    LogUtils.info("遍历报告：${result.report}")
                    
                    assert(result.report.totalPagesFound > 0) {
                        "应该发现至少一个页面"
                    }
                }
                
                is com.superhexa.screenshot.traversal.TraversalResult.Failure -> {
                    LogUtils.error("页面遍历测试失败：${result.error}")
                    throw AssertionError("页面遍历测试失败：${result.error}")
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("页面遍历测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试多状态截图功能
     */
    @Test
    fun testMultiStateCapture() {
        LogUtils.info("开始多状态截图测试")
        
        try {
            // 选择一个测试页面
            val testModule = "home"
            val testFragment = "HomeFragment"
            val testRoute = "/home/<USER>"
            val testStates = listOf("loading", "empty", "success", "error")
            val testDevice = config.devices.phone.first()
            
            // 执行多状态截图
            val screenshots = multiStateCapture.captureAllStatesForPage(
                testModule, testFragment, testRoute, testStates, testDevice
            )
            
            LogUtils.info("多状态截图测试完成，成功截图：${screenshots.size}/${testStates.size}")
            
            assert(screenshots.isNotEmpty()) {
                "至少应该有一张状态截图成功"
            }
            
            // 验证截图文件存在
            screenshots.forEach { screenshot ->
                assert(screenshot.screenshotFile.exists()) {
                    "截图文件应该存在：${screenshot.screenshotFile.absolutePath}"
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("多状态截图测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试设备管理功能
     */
    @Test
    fun testDeviceManagement() {
        LogUtils.info("开始设备管理测试")
        
        try {
            // 获取设备信息
            val deviceInfo = deviceManager.getDeviceInfo()
            LogUtils.deviceInfo(deviceInfo.toString())
            
            // 测试设备配置
            val testDevice = config.devices.phone.first()
            val configured = deviceManager.configureDevice(testDevice)
            
            assert(configured) {
                "设备配置应该成功"
            }
            
            // 验证屏幕尺寸
            val currentSize = deviceManager.getCurrentScreenSize()
            LogUtils.info("当前屏幕尺寸：${currentSize.x}x${currentSize.y}")
            
        } catch (e: Exception) {
            LogUtils.error("设备管理测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试导航功能
     */
    @Test
    fun testNavigation() {
        LogUtils.info("开始导航测试")
        
        try {
            // 测试导航到主页
            val homeNavigated = navigationManager.navigateToFragment("/home/<USER>")
            assert(homeNavigated) {
                "应该能够导航到主页"
            }
            
            // 测试返回功能
            val backSuccess = navigationManager.goBack()
            assert(backSuccess) {
                "返回功能应该成功"
            }
            
            // 测试返回首页
            val homeSuccess = navigationManager.goHome()
            assert(homeSuccess) {
                "返回首页应该成功"
            }
            
        } catch (e: Exception) {
            LogUtils.error("导航测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试状态管理功能
     */
    @Test
    fun testStateManagement() {
        LogUtils.info("开始状态管理测试")
        
        try {
            // 测试各种状态设置
            val testStates = listOf("loading", "empty", "error", "success", "offline", "online")
            
            testStates.forEach { state ->
                val success = stateManager.setState(state)
                LogUtils.stateChange("default", state, success)
                
                // 等待状态生效
                Thread.sleep(1000)
            }
            
            // 恢复默认状态
            val defaultSuccess = stateManager.setState("default")
            assert(defaultSuccess) {
                "应该能够恢复默认状态"
            }
            
        } catch (e: Exception) {
            LogUtils.error("状态管理测试异常", e)
            throw e
        }
    }
    
    /**
     * 测试响应式设计截图
     */
    @Test
    fun testResponsiveScreenshots() {
        LogUtils.info("开始响应式设计截图测试")
        
        try {
            val testModule = "home"
            val testFragment = "HomeFragment"
            val testRoute = "/home/<USER>"
            
            // 测试不同设备尺寸
            val allDevices = config.devices.phone + config.devices.tablet
            
            allDevices.forEach { device ->
                LogUtils.info("测试设备：${device.name}")
                
                // 配置设备
                deviceManager.configureDevice(device)
                
                // 导航到页面
                if (navigationManager.navigateToFragment(testRoute)) {
                    // 截图
                    val screenshotFile = File(
                        config.output.basePath,
                        "${testModule}_${testFragment}_${device.name.replace(" ", "_")}_responsive.png"
                    )
                    
                    val success = uiDevice.takeScreenshot(screenshotFile)
                    LogUtils.screenshot(screenshotFile.name, success, screenshotFile.length())
                    
                    assert(success) {
                        "响应式截图应该成功：${device.name}"
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtils.error("响应式设计截图测试异常", e)
            throw e
        }
    }
    
    // 辅助方法
    
    private fun validateConfiguration() {
        val errors = config.validate()
        if (errors.isNotEmpty()) {
            val errorMessage = "配置验证失败：\n${errors.joinToString("\n")}"
            LogUtils.error(errorMessage)
            throw IllegalArgumentException(errorMessage)
        }
        
        LogUtils.info("配置验证通过")
    }
    
    private fun unlockScreen() {
        try {
            if (!uiDevice.isScreenOn) {
                uiDevice.wakeUp()
                Thread.sleep(1000)
            }
            
            // 如果有锁屏，尝试解锁
            if (uiDevice.hasObject(androidx.test.uiautomator.By.res("com.android.systemui", "keyguard_bottom_area"))) {
                // 向上滑动解锁
                val screenHeight = uiDevice.displayHeight
                val screenWidth = uiDevice.displayWidth
                uiDevice.swipe(screenWidth / 2, screenHeight * 3 / 4, screenWidth / 2, screenHeight / 4, 10)
                Thread.sleep(1000)
            }
            
        } catch (e: Exception) {
            LogUtils.warning("解锁屏幕失败", e)
        }
    }
    
    private fun generateHtmlReport(report: com.superhexa.screenshot.core.ScreenshotReport) {
        try {
            val outputDir = File(config.output.basePath)
            
            // 收集所有截图信息
            val screenshots = mutableListOf<com.superhexa.screenshot.utils.ScreenshotInfo>()
            
            // 这里需要根据实际的截图文件来构建截图信息列表
            // 由于这是示例，我们创建一个简单的报告
            
            val reportFile = FileUtils.createScreenshotReport(
                outputDir,
                screenshots,
                "昆明项目截图报告"
            )
            
            if (reportFile != null) {
                LogUtils.info("HTML报告生成成功：${reportFile.absolutePath}")
            }
            
        } catch (e: Exception) {
            LogUtils.error("生成HTML报告失败", e)
        }
    }
}
