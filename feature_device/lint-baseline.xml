<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.5.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.5.1)" variant="all" version="8.5.1">

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        message="`this` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for com.baidu.baidumaps.extwalkinfo"
        errorLine1="            context.registerReceiver(this, IntentFilter(BaiduMapWalkBroadcast))"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/superhexa/supervision/feature/device/presentation/baidu/broadcastreceiver/BaiduWalkReceiver.kt"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        message="`this` is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected broadcasts registered for android.intent.customize.ACTION_WALK_STOP"
        errorLine1="            context.registerReceiver(this, IntentFilter(ACTION_WALK_STOP))"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/superhexa/supervision/feature/device/presentation/baidu/broadcastreceiver/BaiduWalkStopReceiver.kt"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnsafeImplicitIntentLaunch"
        message="The intent action `android.intent.customize.ACTION_WALK_STOP (used to send a broadcast)` matches the intent filter of a non-exported receiver, registered via a call to `Context.registerReceiver`, or similar. If you are trying to invoke this specific receiver via the action then you should use `Intent.setPackage(&lt;APPLICATION_ID>)`."
        errorLine1="                        sendBroadcast(Intent(ACTION_WALK_STOP))"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/superhexa/supervision/feature/device/presentation/baidu/BaiduWalkService.kt"
            line="105"
            column="39"/>
    </issue>

    <issue
        id="UnsafeImplicitIntentLaunch"
        message="The intent action `android.intent.customize.ACTION_WALK_STOP (used to send a broadcast)` matches the intent filter of a non-exported receiver, registered via a call to `Context.registerReceiver`, or similar. If you are trying to invoke this specific receiver via the action then you should use `Intent.setPackage(&lt;APPLICATION_ID>)`."
        errorLine1="                        sendBroadcast(Intent(ACTION_WALK_STOP))"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/superhexa/supervision/feature/device/presentation/baidu/BaiduWalkService.kt"
            line="119"
            column="39"/>
    </issue>

</issues>
