package com.superhexa.supervision.library.base.log

import android.util.Log
import org.slf4j.LoggerFactory
import timber.log.Timber

class FileLogTree : Timber.DebugTree() {
    //    val ioExecutor = Executors.newSingleThreadExecutor()
    private val logger: org.slf4j.Logger = LoggerFactory.getLogger(this.javaClass)

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (priority == Log.VERBOSE) {
            return
        }

        val logMessage = tag.toString() + ": " + message
        // 将日志打印任务提交给 IO 线程
//        ioExecutor.execute {
        when (priority) {
            Log.DEBUG -> logger.debug(logMessage)
            Log.INFO -> logger.info(logMessage)
            Log.WARN -> logger.warn(logMessage)
            Log.ERROR -> logger.error(logMessage)
        }
//        }
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        return super.createStackElementTag(element) + ":" + element.lineNumber
    }
}
