package com.superhexa.supervision.library.base.log

import android.util.Log
import timber.log.Timber

class ReleaseLogTree : Timber.DebugTree() {

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // Don't log VERBOSE, DEBUG
        if (priority == Log.VERBOSE || priority == Log.DEBUG) {
            return false
        }

        // Log only INFO, ERROR, WARN and WTF
        return true
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        // Add log statements line number to the log
        return super.createStackElementTag(element) + " - " + element.lineNumber
    }
}
