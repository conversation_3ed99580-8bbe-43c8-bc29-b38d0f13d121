package com.superhexa.supervision.library.base.log

import android.content.Context
import android.util.Base64
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

class LogFileCompressor {
    fun getZippedLogFileData(context: Context): String {
        var base64Content = ""
        val logFolderPath = context.getExternalFilesDir(null)?.path + "/log"
        val zipFilePath = context.getExternalFilesDir(null)?.path + "/log.zip"
        zipFolder(logFolderPath, zipFilePath)

        try {
            val file = File(zipFilePath)
            val buffer = ByteArray(file.length().toInt() + BufferExtraCount)
            val fis = FileInputStream(file)
            val length = fis.read(buffer)
            base64Content = Base64.encodeToString(
                buffer,
                0,
                length,
                Base64.NO_WRAP
            )

            fis.close()
            file.delete()
        } catch (e: IOException) {
            Timber.e(e)
        }

        return base64Content
    }

    fun fileDateToBase64(zipFilePath: String): String {
        var base64Content = ""
        try {
            val file = File(zipFilePath)
            val buffer = ByteArray(file.length().toInt() + BufferExtraCount)
            val fis = FileInputStream(file)
            val length = fis.read(buffer)
            base64Content = Base64.encodeToString(
                buffer,
                0,
                length,
                Base64.NO_WRAP
            )

            fis.close()
            file.delete()
        } catch (e: IOException) {
            Timber.e(e)
        }
        return base64Content
    }

    fun getZippedLogData(
        context: Context,
        zipFilePath: String = context.filesDir?.path + "/androidlog.zip"
    ): String {
        return try {
            val logFolderPath = context.filesDir?.path + "/log"
            zipFolder(logFolderPath, zipFilePath)
            File(zipFilePath).absolutePath
        } catch (e: IOException) {
            ""
        }
    }

    private fun zipFolder(inputFolderPath: String, outZipPath: String) {
        try {
            val fos = FileOutputStream(outZipPath)
            val zos = ZipOutputStream(fos)
            val srcFile = File(inputFolderPath)
            val files = srcFile.listFiles()
            Timber.d("Zip directory: %s", srcFile.name)
            if (files == null) {
                Timber.e("log directory is null.")
                return
            }

            for (i in files.indices) {
                Timber.d("Adding file: %s", files[i].name)
                val buffer = ByteArray(BufferSize)
                val fis = FileInputStream(files[i])
                zos.putNextEntry(ZipEntry(files[i].name))
                var length: Int
                while (fis.read(buffer).also { length = it } > 0) {
                    zos.write(buffer, 0, length)
                }
                zos.closeEntry()
                fis.close()
            }
            zos.close()
        } catch (ioe: IOException) {
            Timber.e(ioe)
        }
    }

    companion object {
        const val BufferExtraCount = 100
        const val BufferSize = 1024
    }
}
