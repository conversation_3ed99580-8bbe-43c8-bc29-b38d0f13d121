{"formatVersion": "1.1", "component": {"group": "com.superhexa", "module": "classtrim", "version": "1.0.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.0"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "files": [{"name": "classtrim-1.0.0.jar", "url": "classtrim-1.0.0.jar", "size": 29450, "sha512": "fd61a21f87e7500a93bf5e653eba8dc1d65c67b12f25119b1549c0febd7a2dfcf2735284ec20a8d64961b54542d8fc6be00b5c0e4932aeacd676eb8812e47d82", "sha256": "ba9e833050581e67b0704ea294ce9a5945fffbae1faea416093e46a489e7d1c4", "sha1": "19d17dc9817de50659ce595c29da1128944321fd", "md5": "811f54d1f20d2223c3f53fd4299125c4"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.8.0"}}], "files": [{"name": "classtrim-1.0.0.jar", "url": "classtrim-1.0.0.jar", "size": 29450, "sha512": "fd61a21f87e7500a93bf5e653eba8dc1d65c67b12f25119b1549c0febd7a2dfcf2735284ec20a8d64961b54542d8fc6be00b5c0e4932aeacd676eb8812e47d82", "sha256": "ba9e833050581e67b0704ea294ce9a5945fffbae1faea416093e46a489e7d1c4", "sha1": "19d17dc9817de50659ce595c29da1128944321fd", "md5": "811f54d1f20d2223c3f53fd4299125c4"}]}]}