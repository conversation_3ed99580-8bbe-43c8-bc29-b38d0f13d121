package com.superhexa.supervision.feature.home.presentation.dialog

import android.os.Bundle
import android.view.View
import com.example.feature_home.R
import com.example.feature_home.databinding.FragmentHomeBottomFindoneBinding
import com.superhexa.lib.channel.data.DeviceInfo
import com.superhexa.lib.channel.model.DeviceModelManager.globalModel
import com.superhexa.lib.channel.model.DeviceModelManager.mainlandModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cndModel
import com.superhexa.lib.channel.model.DeviceModelManager.o95cnsModel
import com.superhexa.lib.channel.model.DeviceModelManager.ss2Model
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.model.DeviceModelManager.sssModel
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindAction
import com.superhexa.supervision.feature.home.presentation.bind.DeviceBindDialog
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.fromHtml
import com.superhexa.supervision.library.base.basecommon.extension.visibleOrgone
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.toggle.HexaToggle
import com.superhexa.supervision.library.statistic.O95Statistic
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.constants.EventCons

/**
 * 类描述:绑定逻辑中底部弹框中的找到一个设备时的fragment
 * 创建日期:2021/10/10 on 11:05 上午
 * 作者: FengPeng
 */
class HomeBottomFineOneFragment : InjectionFragment(R.layout.fragment_home_bottom_findone) {
    private var curDeviceInfo: DeviceInfo? = null
    private val viewBinding: FragmentHomeBottomFindoneBinding by viewBinding()
    private var autoConnect: Boolean = false
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.run {
            autoConnect = getBoolean(BundleKey.HOME_BIND_FIND_ONE_DATA)
        }
        viewBinding.tvSeeMore.text = getString(R.string.notYourDevice).fromHtml()
        O95Statistic.exposeTip42998("Device_Binding_FindDevice_End")
    }

    fun setFindOneUIListener(
        deviceInfo: DeviceInfo,
        clickCancelAction: () -> Unit = {},
        action: (DeviceInfo) -> Unit = {}
    ) {
        if (curDeviceInfo != null) return
        curDeviceInfo = deviceInfo
        findOneSubscribeUI(deviceInfo)
        findOneInitListener(deviceInfo, clickCancelAction, action)
    }

    private fun findOneSubscribeUI(deviceInfo: DeviceInfo) {
        viewBinding.ivIcon.setImageResource(
            when (deviceInfo.model) {
                mainlandModel, globalModel -> R.mipmap.device_glass_middle
                o95cnsModel, o95cnModel, o95cndModel -> R.mipmap.o95_find_one
                ssModel -> R.mipmap.ss_find_one
                sssModel -> R.mipmap.sss_find_one
                ss2Model -> R.mipmap.ss2_find_one
                else -> R.mipmap.sss_find_one
            }
        )
        viewBinding.tvDeviceName.text = deviceInfo.name
        viewBinding.tvDeviceId.visibleOrgone(HexaToggle.getDeviceMacIdStatus())
        val macId = "macId:${deviceInfo.getAddress()}"
        viewBinding.tvDeviceId.text = macId
    }

    private fun findOneInitListener(
        deviceInfo: DeviceInfo,
        clickCancelAction: () -> Unit = {}, // 点击取消时候应该 取消扫描
        action: (DeviceInfo) -> Unit = {}
    ) {
        viewBinding.tvCancel.setOnClickListener {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_FindDevice_Begin",
                "found_cancel_button"
            )
            clickCancelAction.invoke()
        }
        viewBinding.tvSeeMore.setOnClickListener {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_FindDevice_Begin",
                "found_more_device"
            )
            StatisticHelper.doEvent(EventCons.EventKey_SV1_CLICK_CHECK_MORE_DEVICE)
            (parentFragment as? DeviceBindDialog)?.dispatchAction(
                DeviceBindAction.FindMoreDevicesAction
            )
        }
        viewBinding.tvConfirm.clickDebounce(viewLifecycleOwner) {
            O95Statistic.clickNameAndPosition(
                "Device_Binding_FindDevice_Begin",
                "found_ok_button"
            )
            action.invoke(deviceInfo)
        }
        if (autoConnect) {
            action.invoke(deviceInfo)
        }
    }

    override fun needDefaultbackground() = false

    companion object {
        fun newInstance(autoConnect: Boolean): HomeBottomFineOneFragment {
            val args = Bundle()
            args.putBoolean(BundleKey.HOME_BIND_FIND_ONE_DATA, autoConnect)
            val fragment = HomeBottomFineOneFragment()
            fragment.arguments = args
            return fragment
        }
    }
}
