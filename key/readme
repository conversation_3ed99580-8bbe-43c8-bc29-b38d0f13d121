
storeFile file('../key/kunmingReleaseKey')
storePassword 'N,Atub3y)&G9xKrRdxg/LR!6#0}kAcq'
keyAlias 'kunmingRelease'
keyPassword 'N,Atub3y)&G9xKrRdxg/LR!6#0}kAcq'
你的密钥库包含 1 个条目

storeFile file('../key/kunming')
            storePassword 'Superhexa'
            keyAlias 'kunming'
            keyPassword 'kunming'
            v1SigningEnabled true
            v2SigningEnabled true

别名: kunming
创建日期: 2021-4-1
条目类型: PrivateKeyEntry
证书链长度: 1
证书[1]:
所有者: CN=Mugger, OU=Software, O=Superhexa, L=Beijing, ST=Beijing, C=CN
发布者: CN=Mugger, OU=Software, O=Superhexa, L=Beijing, ST=Beijing, C=CN
序列号: 2d59607e
生效时间: Thu Apr 01 14:44:39 CST 2021, 失效时间: Sat Mar 08 14:44:39 CST 2121
证书指纹:
	 SHA1: 73:E9:BB:2C:47:EC:8D:C0:15:4C:CB:E9:69:2E:93:4E:D0:1E:A4:14
	 SHA256: F8:E5:DA:36:50:95:5F:90:B7:22:AE:C3:5E:3E:D0:9E:59:BD:E9:9B:3B:67:70:84:03:73:01:83:6B:1B:EB:2E
签名算法名称: SHA256withRSA
主体公共密钥算法: 2048 位 RSA 密钥
版本: 3

扩展:

#1: ObjectId: 2.5.29.14 Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 8F E4 AA 0B E9 D5 04 96   20 4B ED C3 66 35 10 76  ........ K..f5.v
0010: D8 24 5F 0E                                        .$_.
]
]



*******************************************
*******************************************



Warning:
JKS 密钥库使用专用格式。建议使用 "keytool -importkeystore -srckeystore kunming -destkeystore kunming -deststoretype pkcs12" 迁移到行业标准格式 PKCS12。