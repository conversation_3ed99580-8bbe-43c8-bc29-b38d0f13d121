@file:Suppress("ReturnCount", "<PERSON><PERSON><PERSON><PERSON>", "LongMethod", "ComplexMethod", "ComplexCondition")

package com.superhexa.supervision.feature.profile.presentation.feedback.log

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.profile.data.repository.SceneReproductionLogRepository
import com.superhexa.supervision.feature.profile.presentation.feedback.utils.UploadFileUtil
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_IS_RETRY
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_REQUEST_ID_SCENE_LOG
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_REQUEST_TIME_SCENE_LOG
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_START_SCENE_REPRODUCTION_WORK_TIME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_TIME_STAMP_SCENE_LOG
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.base.log.LogFileCompressor
import com.tencent.mmkv.MMKV
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.track.EventTrack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit

class UploadSceneReproductionLogWorker(var context: Context, parameters: WorkerParameters) :
    CoroutineWorker(context, parameters) {

    companion object {
        private const val TAG = "Scene_Reproduction_Worker"
    }

    private val mmkv: MMKV = MMKV.defaultMMKV()
    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val appEnvironment: AppEnvironment by lazy {
        AppEnvironment(instance)
    }
    private val sceneReproductionLogRepository = SceneReproductionLogRepository()
    private val roomZipDirPath =
        instance.getExternalFilesDir("")?.absolutePath + File.separator + "SceneZipLog"
    private val uploadFeedBackLogHelper by lazy { UploadFeedBackLogHelper() }

    override suspend fun doWork(): Result {
        Timber.tag(TAG).d("doWork")
        mmkv.encode(KEY_START_SCENE_REPRODUCTION_WORK_TIME, System.currentTimeMillis())
        inputData.let {
            val model = BlueDeviceDbHelper.getBondDevice()?.model?.toInt() ?: -1
            val isRetry = it.getBoolean(EXTRA_IS_RETRY, false)
            val requestId = it.getString(BundleKey.EXTRA_REQUEST_ID)
            val startTime = it.getString(BundleKey.EXTRA_REQUEST_TIME)
            // 文件传输场景
            val downloading =
                MediaSpaceHandler.isDownloading() || UploadFileUtil.isFileImporting() ||
                    UploadFileUtil.isCollaborationing()
            Timber.tag(TAG).d(
                "model: $model, isRetry: $isRetry," +
                    " requestId: $requestId, startTime: $startTime, downloading: $downloading"
            )

            // 重试机制
            if (isRetry) {
                if (!isExceedSceneLogDeadline()) {
                    Timber.tag(TAG).d(
                        "isKeyguardUnLocked: ${FeedBackUtil.isKeyguardUnLocked(context)}" +
                            ", isNetworkConnected: ${appEnvironment.isNetworkConnected()}"
                    )

                    if (model != -1 && FeedBackUtil.isKeyguardUnLocked(context) &&
                        appEnvironment.isNetworkConnected() && !downloading
                    ) {
                        if (requestId != null && startTime != null) {
                            downloadAndUpLoadSceneLog(
                                model = model.toString(),
                                startTime = startTime,
                                requestId = requestId,
                                successCallBack = {
                                    Timber.tag(TAG).d("upload success")
                                    clearFirstSceneLogRecord()
                                },
                                failCallBack = {
                                    Timber.tag(TAG).d("upload fail")
                                }
                            )
                        }
                        return Result.success()
                    } else {
                        Timber.tag(TAG).d("upload conditions not met.")
                        return Result.failure()
                    }
                } else {
                    clearFirstSceneLogRecord()
                    return Result.failure()
                }
            } else {
                // 首次上传日志
                Timber.tag(TAG)
                    .d("isKeyguardUnLocked: ${FeedBackUtil.isKeyguardUnLocked(context)}")

                if (model != -1 && FeedBackUtil.isKeyguardUnLocked(context) && !downloading) {
                    if (requestId != null && startTime != null) {
                        // 打点复现平台拉取日志，触发反馈时
                        EventTrack.trackFeedBackLogEvent(
                            activityType = "trigger_feedback",
                            requestId = requestId,
                            from = "reproduce_platform_pull"
                        )
                        downloadAndUpLoadSceneLog(
                            model = model.toString(),
                            startTime = startTime,
                            requestId = requestId,
                            successCallBack = {
                                Timber.tag(TAG).d("downloadAndUpLoadSceneLog success")
                            },
                            failCallBack = {
                                Timber.tag(TAG).d("downloadAndUpLoadSceneLog fail")
                                recordFirstSceneLog(requestId, startTime)
                            }
                        )
                    }
                } else {
                    Timber.tag(TAG).d("upload conditions not met.")
                    if (requestId != null && startTime != null) {
                        recordFirstSceneLog(requestId, startTime)
                    }
                }
            }
        }
        return Result.failure()
    }

    private fun recordFirstSceneLog(requestId: String, startTime: String) {
        Timber.tag(TAG).d("recordFirstSceneLog requestId: $requestId")
        mmkv.encode(KEY_TIME_STAMP_SCENE_LOG, System.currentTimeMillis())
        mmkv.encode(KEY_REQUEST_ID_SCENE_LOG, requestId)
        mmkv.encode(KEY_REQUEST_TIME_SCENE_LOG, startTime)
    }

    private fun clearFirstSceneLogRecord() {
        Timber.tag(TAG).d("clearFirstSceneLogRecord")
        mmkv.encode(KEY_TIME_STAMP_SCENE_LOG, 0)
        mmkv.encode(KEY_REQUEST_ID_SCENE_LOG, "")
        mmkv.encode(KEY_REQUEST_TIME_SCENE_LOG, 0)
    }

    // 检查是否超过超时天数
    private fun isExceedSceneLogDeadline(): Boolean {
        val firstAttempt = mmkv.decodeLong(KEY_TIME_STAMP_SCENE_LOG, 0)
        Timber.tag(TAG).d("isExceedSceneLogDeadline firstAttempt: $firstAttempt")
        return (System.currentTimeMillis() - firstAttempt) > TimeUnit.DAYS.toMillis(3)
    }

    private suspend fun downloadAndUpLoadSceneLog(
        model: String,
        startTime: String,
        requestId: String,
        successCallBack: () -> Unit,
        failCallBack: () -> Unit
    ) {
        Timber.tag(TAG).d("downloadAndUpLoadSceneLog")
        uploadFeedBackLogHelper.downloadRoomLog(
            model,
            success = {
                Timber.tag(TAG).d("downloadRoomLog success")

                // 打点复现平台拉取日志，成功拉取固件日志
                EventTrack.trackFeedBackLogEvent(
                    activityType = "pull_firmware_log",
                    requestId = requestId,
                    from = "reproduce_platform_pull"
                )
                uploadSceneLog(
                    startTime = startTime,
                    requestId = requestId,
                    successCallBack = successCallBack,
                    failCallBack = failCallBack
                )
            },
            fail = failCallBack
        )
    }

    private fun uploadSceneLog(
        startTime: String,
        requestId: String,
        successCallBack: () -> Unit,
        failCallBack: () -> Unit
    ) {
        Timber.tag(TAG).d("uploadSceneLog")
        val logs = ArrayList<String>()

        // 获取app 日志
        val appLogPath = LogFileCompressor().getZippedLogData(context)
        logs.add(appLogPath)
        Timber.tag(TAG).d("uploadSceneLog appLogPath: $appLogPath")

        if (uploadFeedBackLogHelper.logPathList.isNotNullOrEmpty()) {
            logs.addAll(uploadFeedBackLogHelper.logPathList)
        }

        FileAndDirUtils.deleteDir(File(roomZipDirPath))
        val filePath = roomZipDirPath + File.separator + requestId + "_logs.zip"
        Timber.tag(TAG).d("uploadSceneLog filePath: $filePath")

        val file = File(filePath)
        FileAndDirUtils.mkdir(File(roomZipDirPath))
        Timber.tag(TAG).d("uploadSceneLog 压缩文件开始")

        val zipFilesSuccess = FeedBackUtil.zipFiles(logs, file.absolutePath)
        Timber.tag(TAG).d(
            "uploadSceneLog 压缩文件是否成功 %s, file path %s",
            zipFilesSuccess,
            file.absolutePath
        )

        // 清除缓存日志文件
        FeedBackUtil.deleteFilesSafely(logs)

        if (zipFilesSuccess) {
            aiCapability.getToken {
                CoroutineScope(Dispatchers.IO).launch {
                    val result = sceneReproductionLogRepository.uploadLogToSceneReproduction(
                        startTime = startTime,
                        requestId = requestId,
                        zipFilePath = file.absolutePath,
                        token = it
                    )
                    Timber.tag(TAG).d("uploadSceneLog result: $result")
                    if (result.isSuccess()) {
                        // 打点复现平台拉取日志，成功上传完整日志
                        EventTrack.trackFeedBackLogEvent(
                            activityType = "upload_complete_log",
                            requestId = requestId,
                            from = "reproduce_platform_pull"
                        )
                        successCallBack.invoke()
                    } else {
                        failCallBack.invoke()
                    }
                }
            }
        }
    }
}
