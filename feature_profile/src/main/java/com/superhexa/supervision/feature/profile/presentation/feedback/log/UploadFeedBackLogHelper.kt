package com.superhexa.supervision.feature.profile.presentation.feedback.log

import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.model.DeviceModelManager.isMijiaO95SeriesDevice
import com.superhexa.lib.channel.model.DeviceModelManager.ssModel
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler.WIFI_P2P
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.profile.presentation.feedback.handler.O95DeviceLogDownloadHelper
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList

class UploadFeedBackLogHelper {

    val logPathList by lazy { CopyOnWriteArrayList<String>() }
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    val model = bondDevice?.model ?: ssModel
    private val o95DeviceLogDownloadHelper by lazy { O95DeviceLogDownloadHelper() }

    companion object {
        private const val SuccessCode = 0
        const val TAG = "UploadFeedBackLogHelper"
    }

    suspend fun downloadRoomLog(
        model: String,
        success: () -> Unit,
        fail: () -> Unit
    ) {
        when {
            isMijiaO95SeriesDevice(model) -> downloadO95RoomLog(
                success = success,
                fail = fail
            )
        }
    }

    private suspend fun downloadO95RoomLog(
        success: () -> Unit,
        fail: () -> Unit
    ) {
        kotlin.runCatching {
            val decorator by lazy { DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice) }
            openMiWearWifiP2P(
                decorator = decorator,
                success = success,
                fail = fail
            )
        }.getOrElse {
            Timber.d("%s downloadO95RoomLog---> %s", it.toString())
        }
    }

    private suspend fun openMiWearWifiP2P(
        decorator: IDeviceOperator<O95StateLiveData>,
        success: () -> Unit,
        fail: () -> Unit
    ) {
        kotlin.runCatching {
            if (decorator.isChannelSuccess()) {
                val startAutoGO = MiWearWiFiP2PConfigHandler.startAutoGO()
                if (!startAutoGO) {
                    Timber.tag(TAG).d("开启Wi-Fi-GO失败")
                    fail.invoke()
                    return
                }

                val result = MiWearWiFiP2PConfigHandler.enableWiFiP2P(client = decorator)
                if (result?.code == SuccessCode && result.ipAddress.isNotNullOrEmpty()) {
                    Timber.tag(TAG).d("开启Wi-Fi-P2P 成功")
                    logPathList.clear()
                    o95DeviceLogDownloadHelper.getO95RoomLogDownloadPath(
                        onSuccess = { path ->
                            logPathList.add(path)
                            CoroutineScope(Dispatchers.Main).launch {
                                closeO95Wifi(decorator)
                            }
                            success.invoke()
                        },
                        onStart = {},
                        onFailed = {
                            Timber.tag(TAG).d("开启Wi-Fi-P2P 失败")
                            CoroutineScope(Dispatchers.Main).launch {
                                closeO95Wifi(decorator)
                            }
                            fail.invoke()
                        }
                    )
                } else {
                    Timber.tag(TAG).d("开启Wi-Fi-P2P 失败")
                    MiWearWiFiP2PConfigHandler.removeGroupIfNeed()
                    fail.invoke()
                }
            } else {
                Timber.tag(TAG).d("createWifi 设备未连接")
                fail.invoke()
            }
        }
    }

    private suspend fun closeO95Wifi(decorator: IDeviceOperator<O95StateLiveData>) {
        val ipAddress = MMKVUtils.decodeString(WIFI_P2P)
        if (DeviceModelManager.isEnableWiFiP2P() && ipAddress.isNotNullOrEmpty()) {
            MiWearWiFiP2PConfigHandler.removeGroupIfNeed()
        } else {
            MiWearWiFiConfigHandler().bindDecorator(decorator)
                .tryDisconnectWifiAp()
        }
    }
}
