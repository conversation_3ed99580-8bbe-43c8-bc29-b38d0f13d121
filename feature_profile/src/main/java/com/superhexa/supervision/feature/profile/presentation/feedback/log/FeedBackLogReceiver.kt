@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.profile.presentation.feedback.log

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.work.BackoffPolicy
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_IS_RETRY
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_REQUEST_ID
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.FEED_EXTRA_TYPE
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_START_FEED_BACK_WORK_TIME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.RETRY_TIME
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import timber.log.Timber
import java.util.concurrent.TimeUnit

class FeedBackLogReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        val isRetry = intent?.getBooleanExtra(EXTRA_IS_RETRY, false)

        if (isRetry == true) {
            scheduleUploadLog(isRetry = true)
        } else {
            val requestId = intent?.getStringExtra(EXTRA_REQUEST_ID)
            val requestType = intent?.getStringExtra(FEED_EXTRA_TYPE)
            scheduleUploadLog(isRetry = false, requestId = requestId, requestType = requestType)
        }
    }

    private fun scheduleUploadLog(
        isRetry: Boolean,
        requestId: String? = "",
        requestType: String? = ""
    ) {
        val lastWorkTime = MMKVUtils.decodeLong(KEY_START_FEED_BACK_WORK_TIME)
        if (System.currentTimeMillis() - lastWorkTime < RETRY_TIME) {
            Timber.tag("last feedback work is running. please retry later.")
            return
        }
        val data = Data.Builder()
            .putString(EXTRA_REQUEST_ID, requestId)
            .putString(FEED_EXTRA_TYPE, requestType)
            .putBoolean(EXTRA_IS_RETRY, isRetry)
            .build()
        val request = OneTimeWorkRequestBuilder<UploadFeedBackLogWorker>()
            .setBackoffCriteria(BackoffPolicy.LINEAR, 5, TimeUnit.MINUTES)
            .setInputData(data)
            .build()

        Timber.d("FeedBackLogReceiver scheduleUploadLog $isRetry, $requestId, $requestType")
        WorkManager.getInstance(instance)
            .enqueueUniqueWork(
                UploadFeedBackLogWorker::class.java.simpleName,
                ExistingWorkPolicy.REPLACE,
                request
            )
    }
}
