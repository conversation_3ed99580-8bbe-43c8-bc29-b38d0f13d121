@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.profile.presentation.feedback.log

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.work.BackoffPolicy
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_IS_RETRY
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_REQUEST_ID
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_REQUEST_TIME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_START_SCENE_REPRODUCTION_WORK_TIME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.RETRY_TIME
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import timber.log.Timber
import java.util.concurrent.TimeUnit

class SceneReproductionLogReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        val isRetry = intent?.getBooleanExtra(EXTRA_IS_RETRY, false)
        val requestId = intent?.getStringExtra(EXTRA_REQUEST_ID)
        val startTime = intent?.getStringExtra(EXTRA_REQUEST_TIME)

        if (isRetry != null) {
            scheduleUploadSceneLog(isRetry, requestId, startTime)
        }
    }

    private fun scheduleUploadSceneLog(isRetry: Boolean, requestId: String?, startTime: String?) {
        val lastWorkTime = MMKVUtils.decodeLong(KEY_START_SCENE_REPRODUCTION_WORK_TIME)
        if (System.currentTimeMillis() - lastWorkTime < RETRY_TIME) {
            Timber.tag("last scene work is running. please retry later.")
            return
        }
        val data = Data.Builder()
            .putString(EXTRA_REQUEST_ID, requestId)
            .putString(EXTRA_REQUEST_TIME, startTime)
            .putBoolean(EXTRA_IS_RETRY, isRetry)
            .build()
        val request = OneTimeWorkRequestBuilder<UploadSceneReproductionLogWorker>()
            .setBackoffCriteria(BackoffPolicy.LINEAR, 5, TimeUnit.MINUTES)
            .setInputData(data)
            .build()

        Timber.d("SceneReproductionLogReceiver scheduleUploadSceneLog $isRetry")
        WorkManager.getInstance(instance)
            .enqueueUniqueWork(
                UploadSceneReproductionLogWorker::class.java.simpleName,
                ExistingWorkPolicy.REPLACE,
                request
            )
    }
}
