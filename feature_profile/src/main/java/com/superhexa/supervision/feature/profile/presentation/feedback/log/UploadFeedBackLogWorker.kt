@file:Suppress("ReturnCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>Method", "ComplexMethod")

package com.superhexa.supervision.feature.profile.presentation.feedback.log

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.profile.data.repository.VoiceFeedBackRepository
import com.superhexa.supervision.feature.profile.presentation.feedback.utils.UploadFileUtil
import com.superhexa.supervision.filetrans.handler.MediaSpaceHandler
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.EXTRA_IS_RETRY
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_FEED_BACK_ID
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_REQUEST_ID
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_START_FEED_BACK_WORK_TIME
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.KEY_TIME_STAMP
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil.readFileToString
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.base.log.LogFileCompressor
import com.tencent.mmkv.MMKV
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.utils.PcmToWavConverter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit

class UploadFeedBackLogWorker(var context: Context, parameters: WorkerParameters) :
    CoroutineWorker(context, parameters) {

    companion object {
        private const val TAG = "Feed_Back_Worker"
    }

    private val mmkv: MMKV = MMKV.defaultMMKV()
    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val appEnvironment: AppEnvironment by lazy {
        AppEnvironment(instance)
    }
    private val voiceFeedRepository = VoiceFeedBackRepository()
    private val roomZipDirPath =
        instance.getExternalFilesDir("")?.absolutePath + File.separator + "roomZipLog"
    private val appZipDirPath =
        instance.getExternalFilesDir("")?.absolutePath + File.separator + "appZipLog"
    private val uploadFeedBackLogHelper by lazy { UploadFeedBackLogHelper() }

    override suspend fun doWork(): Result {
        Timber.tag(TAG).d("doWork")
        mmkv.encode(KEY_START_FEED_BACK_WORK_TIME, System.currentTimeMillis())
        inputData.let {
            val model = BlueDeviceDbHelper.getBondDevice()?.model?.toInt() ?: -1
            val isRetry = it.getBoolean(EXTRA_IS_RETRY, false)
            // 文件传输场景
            val downloading =
                MediaSpaceHandler.isDownloading() || UploadFileUtil.isFileImporting() ||
                    UploadFileUtil.isCollaborationing()

            Timber.tag(TAG).d("model: $model, isRetry: $isRetry, downloading: $downloading")
            // 重试机制
            if (isRetry) {
                if (!isExceedDeadline()) {
                    Timber.tag(TAG)
                        .d(
                            "isKeyguardUnLocked: ${FeedBackUtil.isKeyguardUnLocked(context)}" +
                                ", isNetworkConnected: ${appEnvironment.isNetworkConnected()}"
                        )
                    if (model != -1 &&
                        FeedBackUtil.isKeyguardUnLocked(context) &&
                        appEnvironment.isNetworkConnected()
                    ) {
                        // 获取大小核日志并上传
                        val requestId = mmkv.decodeString(KEY_REQUEST_ID)
                        if (requestId != null && !downloading) {
                            downloadAndUpLoadRoomLog(
                                model.toString(),
                                requestId,
                                successCallBack = { clearFirstFeedBackRecord() },
                                failCallBack = {
                                    Timber.tag(TAG).d("downloadAndUpLoadRoomLog fail")
                                }
                            )
                        }
                        return Result.success()
                    } else {
                        return Result.failure()
                    }
                } else {
                    clearFirstFeedBackRecord()
                    return Result.failure()
                }
            } else {
                // 首次上传日志
                val requestId = it.getString(BundleKey.EXTRA_REQUEST_ID)
                Timber.tag(TAG).d("requestId: $requestId")
                if (requestId != null) {
                    // 打点日志，触发反馈时
                    EventTrack.trackFeedBackLogEvent(
                        activityType = "trigger_feedback",
                        requestId = requestId,
                        from = "query"
                    )

                    // 获取App日志并上传
                    uploadAppLog(requestId)

                    Timber.tag(TAG)
                        .d("isKeyguardUnLocked: ${FeedBackUtil.isKeyguardUnLocked(context)}")
                    if (model != -1 && FeedBackUtil.isKeyguardUnLocked(context) && !downloading) {
                        // 获取大小核日志并上传
                        downloadAndUpLoadRoomLog(
                            model.toString(),
                            requestId,
                            successCallBack = {
                                Timber.tag(TAG).d("downloadAndUpLoadRoomLog success")
                                clearFirstFeedBackRecord()
                            },
                            failCallBack = {
                                Timber.tag(TAG).d("downloadAndUpLoadRoomLog fail")
                                recordFirstFeedBack(requestId)
                            }
                        )
                        return Result.success()
                    } else {
                        Timber.tag(TAG).d("upload conditions not met.")
                        recordFirstFeedBack(requestId)
                        return Result.failure()
                    }
                }
            }
        }
        return Result.failure()
    }

    private fun recordFirstFeedBack(requestId: String) {
        Timber.tag(TAG).d("recordFirstFeedBack requestId: $requestId")
        mmkv.encode(KEY_TIME_STAMP, System.currentTimeMillis())
        mmkv.encode(KEY_REQUEST_ID, requestId)
    }

    private fun recordFirstFeedBackId(feedBackId: String) {
        Timber.tag(TAG).d("recordFirstFeedBackId $feedBackId")
        mmkv.encode(KEY_FEED_BACK_ID, feedBackId)
    }

    private fun clearFirstFeedBackRecord() {
        Timber.tag(TAG).d("clearFirstFeedBackRecord")
        mmkv.encode(KEY_TIME_STAMP, 0)
        mmkv.encode(KEY_REQUEST_ID, "")
        mmkv.encode(KEY_FEED_BACK_ID, "")
    }

    // 检查是否超过超时天数
    private fun isExceedDeadline(): Boolean {
        val firstAttempt = mmkv.decodeLong(KEY_TIME_STAMP, 0)
        Timber.tag(TAG).d("isExceedDeadline firstAttempt: $firstAttempt")
        return (System.currentTimeMillis() - firstAttempt) > TimeUnit.DAYS.toMillis(5)
    }

    private suspend fun downloadAndUpLoadRoomLog(
        model: String,
        requestId: String,
        successCallBack: () -> Unit,
        failCallBack: () -> Unit
    ) {
        withContext(Dispatchers.IO) {
            Timber.tag(TAG).d("downloadAndUpLoadRoomLog")
            uploadFeedBackLogHelper.downloadRoomLog(
                model,
                success = {
                    Timber.tag(TAG).d("downloadRoomLog success")
                    // 打点日志，成功拉取固件日志时时
                    EventTrack.trackFeedBackLogEvent(
                        activityType = "pull_firmware_log",
                        requestId = requestId,
                        from = "query"
                    )
                    CoroutineScope(Dispatchers.IO).launch {
                        uploadRoomLog(
                            requestId,
                            successCallBack = successCallBack,
                            failCallBack = failCallBack
                        )
                    }
                },
                fail = failCallBack
            )
        }
    }

    private suspend fun uploadRoomLog(requestId: String, successCallBack: () -> Unit, failCallBack: () -> Unit) {
        withContext(Dispatchers.IO) {
            Timber.tag(TAG).d("uploadRoomLog")
            val logs = ArrayList<String>()

            if (uploadFeedBackLogHelper.logPathList.isNotNullOrEmpty()) {
                logs.addAll(uploadFeedBackLogHelper.logPathList)
            }

            FileAndDirUtils.deleteDir(File(roomZipDirPath))
            val filePath = roomZipDirPath + File.separator + "log_" + requestId + "_room.zip"
            Timber.tag(TAG).d("uploadRoomLog filePath: $filePath")

            val file = File(filePath)
            FileAndDirUtils.mkdir(File(roomZipDirPath))
            Timber.tag(TAG).d("uploadRoomLog 压缩文件开始")

            val zipFilesSuccess = FeedBackUtil.zipFiles(logs, file.absolutePath)
            val feedbackId = mmkv.decodeString(KEY_FEED_BACK_ID).toString()
            Timber.tag(TAG).d(
                "uploadRoomLog 压缩文件是否成功 %s, file path %s, feedbackId %s",
                zipFilesSuccess,
                file.absolutePath,
                feedbackId
            )

            // 清除缓存日志文件
            FeedBackUtil.deleteFilesSafely(logs)

            if (zipFilesSuccess && feedbackId.isNotNullOrEmpty()) {
                aiCapability.getToken {
                    CoroutineScope(Dispatchers.IO).launch {
                        val result = voiceFeedRepository.uploadFeedback(
                            feedbackId = feedbackId,
                            zipFilePath = file.absolutePath,
                            token = it
                        )
                        if (result.isSuccess()) {
                            // 打点日志，成功上传固件日志时
                            EventTrack.trackFeedBackLogEvent(
                                activityType = "upload_firmware_log",
                                requestId = requestId,
                                from = "query"
                            )
                            successCallBack.invoke()
                        } else {
                            failCallBack.invoke()
                        }
                    }
                }
            }
        }
    }

    private suspend fun uploadAppLog(requestId: String) {
        withContext(Dispatchers.IO) {
            val appLogs = ArrayList<String>()

            // 获取app 日志
            val appLogPath = LogFileCompressor().getZippedLogData(context)
            appLogs.add(appLogPath)
            Timber.tag(TAG).d("uploadAppLog appLogPath: $appLogPath")

            // ASR 文件
            val asrLogPath = context.cacheDir.path + "/FeedBack_$requestId.txt"
            appLogs.add(asrLogPath)
            val asrContent = readFileToString(asrLogPath)
            Timber.tag(TAG).d("uploadAppLog asrLogPath: $asrLogPath, asrContent: $asrContent")

            // 录音文件
            val voicePcmLogPath = context.cacheDir.path + "/VoiceFeedBack_$requestId.pcm"
            val voiceWavLogPath = context.cacheDir.path + "/VoiceFeedBack_$requestId.wav"

            // 将 PCM 格式文件转为 WAV
            PcmToWavConverter.convert(
                pcmPath = voicePcmLogPath,
                wavPath = voiceWavLogPath
            )
            appLogs.add(voicePcmLogPath)
            appLogs.add(voiceWavLogPath)
            Timber.tag(TAG).d(
                "uploadAppLog voicePcmLogPath: $voicePcmLogPath " +
                    "voiceWavLogPath：$voiceWavLogPath"
            )

            FileAndDirUtils.deleteDir(File(appZipDirPath))
            val filePath = appZipDirPath + File.separator + "log_" + requestId + "_app.zip"
            Timber.tag(TAG).d("uploadAppLog filePath: $filePath")

            val file = File(filePath)
            FileAndDirUtils.mkdir(File(appZipDirPath))

            Timber.tag(TAG).d("uploadAppLog 压缩文件开始")
            val zipFilesSuccess = FeedBackUtil.zipFiles(appLogs, file.absolutePath)
            Timber.tag(TAG).d(
                "uploadAppLog 压缩文件是否成功 %s, file path %s",
                zipFilesSuccess,
                file.absolutePath
            )
            if (zipFilesSuccess && asrContent != null) {
                FeedBackUtil.deleteFilesSafely(appLogs)
                aiCapability.getToken {
                    CoroutineScope(Dispatchers.IO).launch {
                        val result = voiceFeedRepository.uploadFeedback(
                            feedbackContent = asrContent,
                            zipFilePath = file.absolutePath,
                            token = it
                        )

                        // 延迟5s 避免与上传TTS声音重叠
                        delay(5000)

                        if (result.isSuccess() && result.data?.result != null) {
                            // 打点日志，成功上传app日志时时
                            EventTrack.trackFeedBackLogEvent(
                                activityType = "upload_app_log",
                                requestId = requestId,
                                from = "query"
                            )

                            // 播放TTS
                            Timber.tag(TAG).d("uploadAppLog Success 播放反馈成功TTS")
                            AiSpeechEngine.INSTANCE.startTts("反馈成功，我们会尽快帮您解决")
                            result.data?.result?.get("feedbackId")
                                ?.let { it1 -> recordFirstFeedBackId(it1) }
                        } else {
                            Timber.tag(TAG).d("uploadAppLog fail 播放反馈失败TTS, result: $result")
                            AiSpeechEngine.INSTANCE.startTts("上传失败，请重新反馈一下吧")
                        }
                    }
                }
            }
        }
    }
}
